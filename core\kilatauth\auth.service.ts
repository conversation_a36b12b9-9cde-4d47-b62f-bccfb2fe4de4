/**
 * KilatAuth - Authentication Service
 * Complete authentication system with JWT, RBAC, and security features
 */

import { KilatJWT, AuthTokens, AuthUser } from './jwt'
import { KilatRBAC, AccessContext } from './rbac'
import { KilatSecurity } from './security'
import { KilatORM } from '../kilatorm'

export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  email: string
  password: string
  name: string
  role?: string
}

export interface AuthResult {
  success: boolean
  user?: AuthUser
  tokens?: AuthTokens
  error?: string
}

export interface SessionData {
  id: string
  userId: string
  ip: string
  userAgent: string
  createdAt: Date
  lastActivity: Date
  isActive: boolean
}

export class KilatAuthService {
  private jwt: KilatJWT
  private rbac: KilatRBAC
  private security: KilatSecurity
  private orm: KilatORM
  private sessions: Map<string, SessionData> = new Map()

  constructor() {
    this.jwt = new KilatJWT()
    this.rbac = new KilatRBAC()
    this.security = new KilatSecurity()
    this.orm = new KilatORM()
    this.orm.connect()
  }

  /**
   * User login
   */
  async login(credentials: LoginCredentials, req: any): Promise<AuthResult> {
    try {
      // Security validation
      const validation = this.security.validateRequest(req)
      if (!validation.valid) {
        return {
          success: false,
          error: validation.errors.join(', ')
        }
      }

      // Find user by email
      const user = await this.orm.users.findOne({ email: credentials.email })
      if (!user) {
        return {
          success: false,
          error: 'Invalid credentials'
        }
      }

      // Verify password
      const isValidPassword = this.jwt.verifyPassword(
        credentials.password,
        user.passwordHash,
        user.passwordSalt || ''
      )

      if (!isValidPassword) {
        return {
          success: false,
          error: 'Invalid credentials'
        }
      }

      // Check if user is active
      if (user.status !== 'active') {
        return {
          success: false,
          error: 'Account is not active'
        }
      }

      // Get user permissions
      const permissions = this.rbac.getRolePermissions(user.role)
      const permissionIds = permissions.map(p => p.id)

      // Create auth user
      const authUser: AuthUser = {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        permissions: permissionIds,
        lastLoginAt: new Date(),
        isActive: true
      }

      // Generate tokens
      const tokens = this.jwt.generateTokens(authUser)

      // Create session
      const sessionId = this.security.generateSessionId()
      const session: SessionData = {
        id: sessionId,
        userId: user.id,
        ip: this.security.getClientIP(req),
        userAgent: req.headers['user-agent'] || '',
        createdAt: new Date(),
        lastActivity: new Date(),
        isActive: true
      }
      this.sessions.set(sessionId, session)

      // Update last login
      await this.orm.users.updateOne(
        { id: user.id },
        { lastLoginAt: new Date() }
      )

      return {
        success: true,
        user: authUser,
        tokens
      }

    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        error: 'Login failed'
      }
    }
  }

  /**
   * User registration
   */
  async register(data: RegisterData): Promise<AuthResult> {
    try {
      // Check if user already exists
      const existingUser = await this.orm.users.findOne({ email: data.email })
      if (existingUser) {
        return {
          success: false,
          error: 'User already exists'
        }
      }

      // Hash password
      const { hash, salt } = this.jwt.hashPassword(data.password)

      // Create user
      const newUser = await this.orm.users.create({
        id: `user_${Date.now()}`,
        email: data.email,
        name: data.name,
        role: data.role || 'user',
        passwordHash: hash,
        passwordSalt: salt,
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      })

      // Get user permissions
      const permissions = this.rbac.getRolePermissions(newUser.role)
      const permissionIds = permissions.map(p => p.id)

      // Create auth user
      const authUser: AuthUser = {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
        role: newUser.role,
        permissions: permissionIds,
        lastLoginAt: new Date(),
        isActive: true
      }

      // Generate tokens
      const tokens = this.jwt.generateTokens(authUser)

      return {
        success: true,
        user: authUser,
        tokens
      }

    } catch (error) {
      console.error('Registration error:', error)
      return {
        success: false,
        error: 'Registration failed'
      }
    }
  }

  /**
   * Verify access token and get user
   */
  async verifyToken(token: string): Promise<AuthUser | null> {
    try {
      const payload = this.jwt.verifyAccessToken(token)
      if (!payload) return null

      // Get user from database
      const user = await this.orm.users.findById(payload.sub)
      if (!user || user.status !== 'active') return null

      // Get current permissions
      const permissions = this.rbac.getRolePermissions(user.role)
      const permissionIds = permissions.map(p => p.id)

      return {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        permissions: permissionIds,
        lastLoginAt: user.lastLoginAt ? new Date(user.lastLoginAt) : new Date(),
        isActive: true
      }

    } catch (error) {
      console.error('Token verification error:', error)
      return null
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<{ accessToken: string } | null> {
    try {
      const newAccessToken = this.jwt.refreshAccessToken(refreshToken)
      if (!newAccessToken) return null

      return { accessToken: newAccessToken }

    } catch (error) {
      console.error('Token refresh error:', error)
      return null
    }
  }

  /**
   * Logout user
   */
  async logout(token: string, sessionId?: string): Promise<boolean> {
    try {
      // Revoke token
      this.jwt.revokeToken(token)

      // Remove session
      if (sessionId) {
        this.sessions.delete(sessionId)
      }

      return true

    } catch (error) {
      console.error('Logout error:', error)
      return false
    }
  }

  /**
   * Check if user can access resource
   */
  canAccess(user: AuthUser, resource: string, action: string, data?: any): boolean {
    const context: AccessContext = {
      userId: user.id,
      userRole: user.role,
      userPermissions: user.permissions,
      resource,
      action,
      data,
      timestamp: new Date()
    }

    return this.rbac.canAccess(context)
  }

  /**
   * Get user permissions
   */
  getUserPermissions(roleId: string): string[] {
    const permissions = this.rbac.getRolePermissions(roleId)
    return permissions.map(p => p.id)
  }

  /**
   * Update user role
   */
  async updateUserRole(userId: string, newRole: string): Promise<boolean> {
    try {
      if (!this.rbac.roleExists(newRole)) {
        return false
      }

      await this.orm.users.updateOne(
        { id: userId },
        { role: newRole, updatedAt: new Date() }
      )

      return true

    } catch (error) {
      console.error('Update user role error:', error)
      return false
    }
  }

  /**
   * Get active sessions for user
   */
  getUserSessions(userId: string): SessionData[] {
    return Array.from(this.sessions.values())
      .filter(session => session.userId === userId && session.isActive)
  }

  /**
   * Revoke user session
   */
  revokeSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId)
    if (session) {
      session.isActive = false
      return true
    }
    return false
  }

  /**
   * Clean expired sessions
   */
  cleanExpiredSessions(): void {
    const now = Date.now()
    const maxAge = 24 * 60 * 60 * 1000 // 24 hours

    for (const [sessionId, session] of this.sessions.entries()) {
      if (now - session.lastActivity.getTime() > maxAge) {
        this.sessions.delete(sessionId)
      }
    }
  }

  /**
   * Get security instance
   */
  getSecurity(): KilatSecurity {
    return this.security
  }

  /**
   * Get RBAC instance
   */
  getRBAC(): KilatRBAC {
    return this.rbac
  }
}
