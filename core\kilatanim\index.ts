/**
 * KilatAnim - Advanced animation system for Kilat.js
 * Provides smooth animations, transitions, and effects
 */

export interface AnimationConfig {
  duration?: number
  delay?: number
  easing?: string
  iterations?: number | 'infinite'
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse'
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both'
}

export interface AnimationPreset {
  name: string
  keyframes: Keyframe[]
  config: AnimationConfig
}

export class KilatAnim {
  private static instance: KilatAnim
  private animations: Map<string, Animation> = new Map()
  private presets: Map<string, AnimationPreset> = new Map()
  private observers: Map<string, IntersectionObserver> = new Map()

  constructor() {
    this.registerBuiltinPresets()
    this.setupIntersectionObserver()
  }

  static getInstance(): KilatAnim {
    if (!KilatAnim.instance) {
      KilatAnim.instance = new KilatAnim()
    }
    return KilatAnim.instance
  }

  /**
   * Register built-in animation presets
   */
  private registerBuiltinPresets(): void {
    // Fade animations
    this.registerPreset('fadeIn', {
      name: 'fadeIn',
      keyframes: [
        { opacity: 0, transform: 'translateY(20px)' },
        { opacity: 1, transform: 'translateY(0)' }
      ],
      config: { duration: 600, easing: 'ease-out' }
    })

    this.registerPreset('fadeOut', {
      name: 'fadeOut',
      keyframes: [
        { opacity: 1, transform: 'translateY(0)' },
        { opacity: 0, transform: 'translateY(-20px)' }
      ],
      config: { duration: 400, easing: 'ease-in' }
    })

    // Slide animations
    this.registerPreset('slideUp', {
      name: 'slideUp',
      keyframes: [
        { transform: 'translateY(100%)', opacity: 0 },
        { transform: 'translateY(0)', opacity: 1 }
      ],
      config: { duration: 500, easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)' }
    })

    this.registerPreset('slideDown', {
      name: 'slideDown',
      keyframes: [
        { transform: 'translateY(-100%)', opacity: 0 },
        { transform: 'translateY(0)', opacity: 1 }
      ],
      config: { duration: 500, easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)' }
    })

    this.registerPreset('slideLeft', {
      name: 'slideLeft',
      keyframes: [
        { transform: 'translateX(100%)', opacity: 0 },
        { transform: 'translateX(0)', opacity: 1 }
      ],
      config: { duration: 500, easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)' }
    })

    this.registerPreset('slideRight', {
      name: 'slideRight',
      keyframes: [
        { transform: 'translateX(-100%)', opacity: 0 },
        { transform: 'translateX(0)', opacity: 1 }
      ],
      config: { duration: 500, easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)' }
    })

    // Scale animations
    this.registerPreset('scaleIn', {
      name: 'scaleIn',
      keyframes: [
        { transform: 'scale(0.8)', opacity: 0 },
        { transform: 'scale(1)', opacity: 1 }
      ],
      config: { duration: 400, easing: 'cubic-bezier(0.34, 1.56, 0.64, 1)' }
    })

    this.registerPreset('scaleOut', {
      name: 'scaleOut',
      keyframes: [
        { transform: 'scale(1)', opacity: 1 },
        { transform: 'scale(0.8)', opacity: 0 }
      ],
      config: { duration: 300, easing: 'ease-in' }
    })

    // Bounce animations
    this.registerPreset('bounceIn', {
      name: 'bounceIn',
      keyframes: [
        { transform: 'scale(0.3)', opacity: 0 },
        { transform: 'scale(1.05)', opacity: 0.7 },
        { transform: 'scale(0.9)', opacity: 0.9 },
        { transform: 'scale(1)', opacity: 1 }
      ],
      config: { duration: 800, easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)' }
    })

    // Glow animations
    this.registerPreset('glow', {
      name: 'glow',
      keyframes: [
        { boxShadow: '0 0 5px var(--kilat-primary)' },
        { boxShadow: '0 0 20px var(--kilat-primary), 0 0 30px var(--kilat-primary)' },
        { boxShadow: '0 0 5px var(--kilat-primary)' }
      ],
      config: { duration: 2000, iterations: 'infinite', easing: 'ease-in-out' }
    })

    // Float animation
    this.registerPreset('float', {
      name: 'float',
      keyframes: [
        { transform: 'translateY(0px)' },
        { transform: 'translateY(-10px)' },
        { transform: 'translateY(0px)' }
      ],
      config: { duration: 3000, iterations: 'infinite', easing: 'ease-in-out' }
    })

    // Pulse animation
    this.registerPreset('pulse', {
      name: 'pulse',
      keyframes: [
        { transform: 'scale(1)', opacity: 1 },
        { transform: 'scale(1.05)', opacity: 0.8 },
        { transform: 'scale(1)', opacity: 1 }
      ],
      config: { duration: 2000, iterations: 'infinite', easing: 'ease-in-out' }
    })

    // Rotate animations
    this.registerPreset('rotateIn', {
      name: 'rotateIn',
      keyframes: [
        { transform: 'rotate(-180deg) scale(0.8)', opacity: 0 },
        { transform: 'rotate(0deg) scale(1)', opacity: 1 }
      ],
      config: { duration: 600, easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)' }
    })

    // Flip animations
    this.registerPreset('flipIn', {
      name: 'flipIn',
      keyframes: [
        { transform: 'perspective(400px) rotateY(-90deg)', opacity: 0 },
        { transform: 'perspective(400px) rotateY(0deg)', opacity: 1 }
      ],
      config: { duration: 600, easing: 'ease-out' }
    })
  }

  /**
   * Register animation preset
   */
  registerPreset(name: string, preset: AnimationPreset): void {
    this.presets.set(name, preset)
  }

  /**
   * Animate element with preset
   */
  animate(element: Element, presetName: string, customConfig?: Partial<AnimationConfig>): Animation | null {
    const preset = this.presets.get(presetName)
    if (!preset) {
      console.warn(`Animation preset "${presetName}" not found`)
      return null
    }

    const config = { ...preset.config, ...customConfig }
    const animation = element.animate(preset.keyframes, {
      duration: config.duration,
      delay: config.delay,
      easing: config.easing,
      iterations: config.iterations,
      direction: config.direction,
      fill: config.fillMode
    })

    const animationId = `${presetName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    this.animations.set(animationId, animation)

    // Clean up when animation finishes
    animation.addEventListener('finish', () => {
      this.animations.delete(animationId)
    })

    return animation
  }

  /**
   * Animate element on scroll into view
   */
  animateOnScroll(element: Element, presetName: string, options?: {
    threshold?: number
    rootMargin?: string
    once?: boolean
  }): void {
    const { threshold = 0.1, rootMargin = '0px', once = true } = options || {}

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.animate(entry.target, presetName)
          if (once) {
            observer.unobserve(entry.target)
          }
        }
      })
    }, { threshold, rootMargin })

    observer.observe(element)
    this.observers.set(element.id || `element_${Date.now()}`, observer)
  }

  /**
   * Setup intersection observer for auto-animations
   */
  private setupIntersectionObserver(): void {
    if (typeof window === 'undefined') return

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const element = entry.target as HTMLElement
          const animationType = element.dataset.kilatAnim
          if (animationType) {
            this.animate(element, animationType)
            observer.unobserve(element)
          }
        }
      })
    }, { threshold: 0.1 })

    // Observe all elements with data-kilat-anim attribute
    document.addEventListener('DOMContentLoaded', () => {
      const animatedElements = document.querySelectorAll('[data-kilat-anim]')
      animatedElements.forEach(element => observer.observe(element))
    })
  }

  /**
   * Create custom animation
   */
  createCustomAnimation(
    element: Element,
    keyframes: Keyframe[],
    config: AnimationConfig
  ): Animation {
    return element.animate(keyframes, {
      duration: config.duration,
      delay: config.delay,
      easing: config.easing,
      iterations: config.iterations,
      direction: config.direction,
      fill: config.fillMode
    })
  }

  /**
   * Stop all animations
   */
  stopAllAnimations(): void {
    this.animations.forEach(animation => animation.cancel())
    this.animations.clear()
  }

  /**
   * Get all available presets
   */
  getPresets(): string[] {
    return Array.from(this.presets.keys())
  }
}

// Export singleton instance
export const kilatAnim = KilatAnim.getInstance()

// Export utility functions
export const animate = (element: Element, preset: string, config?: Partial<AnimationConfig>) => 
  kilatAnim.animate(element, preset, config)

export const animateOnScroll = (element: Element, preset: string, options?: any) => 
  kilatAnim.animateOnScroll(element, preset, options)
