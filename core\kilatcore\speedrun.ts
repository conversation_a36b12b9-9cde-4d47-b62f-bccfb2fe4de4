/**
 * SpeedRun Runtime - Core runtime engine for Kilat.js
 * Native HTTP server with Bun.js + Node.js fallback
 * Features: SSR, Hot Reload, Error Handling, Static Assets
 */

import { serve } from 'bun'
import { createServer, IncomingMessage, ServerResponse } from 'http'
import { readFileSync, existsSync, statSync, watch } from 'fs'
import { join, extname } from 'path'
import { KilatConfig, HotReloadEvent, StaticAsset } from './types'
import { AppRouter } from './router'
import { PluginManager } from '../kilatplugin/manager'
import { MiddlewareProcessor } from './middleware'
import { SSRRenderer } from '../kilatstate/ssr'
import { ErrorOverlay } from '../kilaterror/overlay'

export class SpeedRunRuntime {
  private config: KilatConfig
  private router: AppRouter
  private pluginManager: PluginManager
  private middlewareProcessor: MiddlewareProcessor
  private ssrRenderer: SSRRenderer
  private errorOverlay: ErrorOverlay
  private server: any
  private staticAssets: Map<string, StaticAsset> = new Map()
  private hotReloadClients: Set<WebSocket> = new Set()
  private fileWatcher: any
  private isDevelopment: boolean

  constructor(config: KilatConfig) {
    this.config = config
    this.router = new AppRouter(config)
    this.pluginManager = new PluginManager(config)
    this.middlewareProcessor = new MiddlewareProcessor(config)
    this.ssrRenderer = new SSRRenderer(config)
    this.errorOverlay = new ErrorOverlay(config)
    this.isDevelopment = process.env.NODE_ENV === 'development'
  }
  
  /**
   * Initialize the runtime
   */
  async initialize(): Promise<void> {
    console.log('🚀 Initializing SpeedRun Runtime...')

    // Load plugins
    await this.pluginManager.loadPlugins()

    // Initialize router
    await this.router.initialize()

    // Initialize middleware
    await this.middlewareProcessor.initialize()

    // Initialize SSR renderer
    await this.ssrRenderer.initialize()

    // Load static assets
    await this.loadStaticAssets()

    // Setup file watching for development
    if (this.isDevelopment) {
      await this.setupFileWatcher()
    }

    console.log('✅ SpeedRun Runtime initialized')
  }
  
  /**
   * Start the server
   */
  async start(): Promise<void> {
    const { runtime } = this.config

    // Try Bun.js first if specified or auto
    if (runtime.engine === 'bun' || runtime.engine === 'auto') {
      try {
        await this.startBunServer()
        return
      } catch (error) {
        console.warn('⚠️ Bun.js not available, falling back to Node.js')
        // Continue to Node.js fallback
      }
    }

    // Try Node.js (either specified, auto, or fallback from Bun)
    console.log(`🔍 Runtime engine: ${runtime.engine}`)
    if (runtime.engine === 'node' || runtime.engine === 'auto' || runtime.engine === 'bun') {
      try {
        console.log('🚀 Starting Node.js server...')
        await this.startNodeServer()
        return
      } catch (error) {
        console.error('❌ Failed to start Node.js server:', error)
        throw error
      }
    }

    throw new Error('No suitable runtime engine available')
  }
  
  /**
   * Start Bun.js server
   */
  private async startBunServer(): Promise<void> {
    const { runtime } = this.config
    
    this.server = serve({
      port: runtime.port,
      hostname: runtime.host,
      fetch: this.handleRequest.bind(this),
      error: this.handleError.bind(this),
    })
    
    console.log(`⚡ SpeedRun server running on http://${runtime.host}:${runtime.port} (Bun.js)`)
  }
  
  /**
   * Start Node.js server
   */
  private async startNodeServer(): Promise<void> {
    const { runtime } = this.config

    this.server = createServer(async (req: IncomingMessage, res: ServerResponse) => {
      try {
        const request = await this.nodeRequestToFetch(req)
        const response = await this.handleRequest(request)
        await this.sendNodeResponse(res, response)
      } catch (error) {
        this.handleError(error as Error)
        res.statusCode = 500
        res.end('Internal Server Error')
      }
    })
    
    this.server.listen(runtime.port, runtime.host, () => {
      console.log(`⚡ SpeedRun server running on http://${runtime.host}:${runtime.port} (Node.js)`)
    })
  }
  
  /**
   * Handle incoming requests
   */
  private async handleRequest(request: Request): Promise<Response> {
    const startTime = Date.now()
    const url = new URL(request.url)

    try {
      // Handle hot reload WebSocket connections
      if (this.isDevelopment && url.pathname === '/__kilat_hmr') {
        return this.handleHotReloadConnection(request)
      }

      // Handle static assets
      if (url.pathname.startsWith('/public/') || url.pathname.startsWith('/_kilat/')) {
        const staticResponse = await this.handleStaticAsset(request)
        if (staticResponse) {
          return staticResponse
        }
      }

      // Process middleware
      const middlewareResult = await this.middlewareProcessor.process(request)
      if (middlewareResult) {
        return middlewareResult
      }

      // Route the request
      const response = await this.router.handle(request)

      // Add default headers
      this.addDefaultHeaders(response)

      // Add performance headers
      const duration = Date.now() - startTime
      response.headers.set('X-Response-Time', `${duration}ms`)

      return response

    } catch (error) {
      console.error('Request handling error:', error)

      // Return error overlay in development
      if (this.isDevelopment) {
        return this.errorOverlay.renderError(error as Error, request)
      }

      return new Response('Internal Server Error', {
        status: 500,
        headers: { 'Content-Type': 'text/plain' }
      })
    }
  }
  
  /**
   * Handle errors
   */
  private handleError(error: Error): void {
    console.error('SpeedRun Runtime Error:', error)
  }
  
  /**
   * Convert Node.js request to Fetch API request
   */
  private nodeRequestToFetch(req: IncomingMessage): Request {
    const url = `http://${req.headers.host}${req.url}`
    const headers = new Headers()

    for (const [key, value] of Object.entries(req.headers)) {
      if (typeof value === 'string') {
        headers.set(key, value)
      } else if (Array.isArray(value)) {
        headers.set(key, value.join(', '))
      }
    }

    return new Request(url, {
      method: req.method || 'GET',
      headers,
      body: req.method !== 'GET' && req.method !== 'HEAD' ? req as any : undefined,
    })
  }
  
  /**
   * Send response to Node.js response object
   */
  private async sendNodeResponse(res: ServerResponse, response: Response): Promise<void> {
    res.statusCode = response.status

    // Set headers
    response.headers.forEach((value, key) => {
      res.setHeader(key, value)
    })

    // Send body
    if (response.body) {
      const reader = response.body.getReader()
      while (true) {
        const { done, value } = await reader.read()
        if (done) break
        res.write(value)
      }
    }

    res.end()
  }
  
  /**
   * Add default headers to response
   */
  private addDefaultHeaders(response: Response): void {
    if (!response.headers.has('X-Powered-By')) {
      response.headers.set('X-Powered-By', 'Kilat.js SpeedRun')
    }
    
    if (!response.headers.has('X-Runtime')) {
      response.headers.set('X-Runtime', this.config.runtime.engine)
    }
  }
  
  /**
   * Stop the server
   */
  async stop(): Promise<void> {
    if (this.server) {
      if (typeof this.server.stop === 'function') {
        // Bun.js server
        this.server.stop()
      } else {
        // Node.js server
        this.server.close()
      }
      console.log('🛑 SpeedRun server stopped')
    }
  }
  
  /**
   * Reload the server (for development)
   */
  async reload(): Promise<void> {
    console.log('🔄 Reloading SpeedRun server...')
    await this.stop()
    await this.initialize()
    await this.start()
  }
  
  /**
   * Handle hot reload WebSocket connections
   */
  private async handleHotReloadConnection(request: Request): Promise<Response> {
    if (request.headers.get('upgrade') !== 'websocket') {
      return new Response('Expected WebSocket', { status: 400 })
    }

    // This would be implemented with proper WebSocket handling
    // For now, return a placeholder response
    return new Response('Hot reload WebSocket endpoint', { status: 200 })
  }

  /**
   * Handle static asset requests
   */
  private async handleStaticAsset(request: Request): Promise<Response | null> {
    const url = new URL(request.url)
    const pathname = url.pathname

    // Check if asset exists in cache
    if (this.staticAssets.has(pathname)) {
      const asset = this.staticAssets.get(pathname)!

      // Check if-none-match header for caching
      const ifNoneMatch = request.headers.get('if-none-match')
      if (ifNoneMatch === asset.etag) {
        return new Response(null, { status: 304 })
      }

      return new Response(asset.content, {
        headers: {
          'Content-Type': asset.contentType,
          'ETag': asset.etag,
          'Last-Modified': asset.lastModified.toUTCString(),
          'Cache-Control': this.isDevelopment ? 'no-cache' : 'public, max-age=31536000',
        }
      })
    }

    // Try to serve from public directory
    const publicPath = join(process.cwd(), 'public', pathname.replace('/public/', ''))
    if (existsSync(publicPath)) {
      try {
        const content = readFileSync(publicPath)
        const stats = statSync(publicPath)
        const contentType = this.getContentType(extname(publicPath))
        const etag = `"${stats.mtime.getTime()}-${stats.size}"`

        const asset: StaticAsset = {
          path: pathname,
          content,
          contentType,
          etag,
          lastModified: stats.mtime
        }

        // Cache the asset
        this.staticAssets.set(pathname, asset)

        return new Response(content, {
          headers: {
            'Content-Type': contentType,
            'ETag': etag,
            'Last-Modified': stats.mtime.toUTCString(),
            'Cache-Control': this.isDevelopment ? 'no-cache' : 'public, max-age=31536000',
          }
        })
      } catch (error) {
        console.error('Error serving static asset:', error)
      }
    }

    return null
  }

  /**
   * Load static assets into memory
   */
  private async loadStaticAssets(): Promise<void> {
    const publicDir = join(process.cwd(), 'public')
    if (!existsSync(publicDir)) {
      return
    }

    // This would recursively scan the public directory
    // and load assets into memory for faster serving
    console.log('📁 Loading static assets...')
  }

  /**
   * Setup file watcher for hot reload
   */
  private async setupFileWatcher(): Promise<void> {
    if (!this.isDevelopment) return

    console.log('👀 Setting up file watcher for hot reload...')

    const watchPaths = [
      this.config.apps.dir,
      'components',
      'core',
      'public'
    ]

    for (const watchPath of watchPaths) {
      if (existsSync(watchPath)) {
        this.fileWatcher = watch(watchPath, { recursive: true }, (eventType: string, filename: string | null) => {
          if (filename) {
            this.handleFileChange(eventType, filename)
          }
        })
      }
    }
  }

  /**
   * Handle file changes for hot reload
   */
  private handleFileChange(eventType: string, filename: string): void {
    const event: HotReloadEvent = {
      type: eventType === 'rename' ? 'update' : 'update',
      path: filename,
      timestamp: Date.now()
    }

    console.log(`🔄 File changed: ${filename} (${eventType})`)

    // Broadcast to all connected hot reload clients
    this.broadcastHotReload(event)

    // Clear relevant caches
    this.clearCaches(filename)
  }

  /**
   * Broadcast hot reload event to connected clients
   */
  private broadcastHotReload(event: HotReloadEvent): void {
    const message = JSON.stringify(event)

    for (const client of this.hotReloadClients) {
      try {
        client.send(message)
      } catch (error) {
        // Remove disconnected clients
        this.hotReloadClients.delete(client)
      }
    }
  }

  /**
   * Clear caches when files change
   */
  private clearCaches(filename: string): void {
    // Clear static asset cache if public file changed
    if (filename.startsWith('public/')) {
      const assetPath = '/' + filename
      this.staticAssets.delete(assetPath)
    }

    // Clear module cache for hot reload
    if (filename.endsWith('.ts') || filename.endsWith('.tsx') || filename.endsWith('.js') || filename.endsWith('.jsx')) {
      // This would clear Node.js module cache
      // delete require.cache[require.resolve(filename)]
    }
  }

  /**
   * Convert Node.js request to Fetch API request
   */
  private async nodeRequestToFetch(req: IncomingMessage): Promise<Request> {
    const protocol = req.socket.encrypted ? 'https:' : 'http:'
    const host = req.headers.host || 'localhost'
    const url = `${protocol}//${host}${req.url}`

    const headers = new Headers()
    for (const [key, value] of Object.entries(req.headers)) {
      if (value) {
        if (Array.isArray(value)) {
          value.forEach(v => headers.append(key, v))
        } else {
          headers.set(key, value)
        }
      }
    }

    const init: RequestInit = {
      method: req.method || 'GET',
      headers,
    }

    // Add body for POST/PUT/PATCH requests
    if (req.method && ['POST', 'PUT', 'PATCH'].includes(req.method)) {
      const chunks: Buffer[] = []

      return new Promise((resolve, reject) => {
        req.on('data', (chunk: Buffer) => {
          chunks.push(chunk)
        })

        req.on('end', () => {
          if (chunks.length > 0) {
            init.body = Buffer.concat(chunks).toString()
          }
          resolve(new Request(url, init))
        })

        req.on('error', reject)
      })
    }

    return new Request(url, init)
  }

  /**
   * Send Fetch API response to Node.js response
   */
  private async sendNodeResponse(res: ServerResponse, response: Response): Promise<void> {
    // Set status code
    res.statusCode = response.status

    // Set headers
    response.headers.forEach((value, key) => {
      res.setHeader(key, value)
    })

    // Send body
    if (response.body) {
      try {
        const text = await response.text()
        res.write(text)
      } catch (error) {
        console.error('Error reading response body:', error)
      }
    }

    res.end()
  }

  /**
   * Get content type for file extension
   */
  private getContentType(ext: string): string {
    const mimeTypes: Record<string, string> = {
      '.html': 'text/html',
      '.css': 'text/css',
      '.js': 'application/javascript',
      '.json': 'application/json',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.ico': 'image/x-icon',
      '.woff': 'font/woff',
      '.woff2': 'font/woff2',
      '.ttf': 'font/ttf',
      '.eot': 'application/vnd.ms-fontobject',
    }

    return mimeTypes[ext] || 'application/octet-stream'
  }

  /**
   * Get server status
   */
  getStatus(): any {
    return {
      runtime: this.config.runtime.engine,
      port: this.config.runtime.port,
      host: this.config.runtime.host,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version,
      isDevelopment: this.isDevelopment,
      hotReloadClients: this.hotReloadClients.size,
      staticAssets: this.staticAssets.size,
    }
  }
}
