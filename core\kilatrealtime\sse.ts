/**
 * KilatRealtime - Server-Sent Events
 * Fallback real-time system using SSE
 */

import { randomBytes } from 'crypto'

export interface SSEConnection {
  id: string
  response: any
  channels: Set<string>
  lastActivity: Date
  metadata: {
    ip: string
    userAgent: string
    connectedAt: Date
  }
}

export interface SSEMessage {
  id: string
  event?: string
  data: any
  retry?: number
}

export class KilatSSEServer {
  private connections: Map<string, SSEConnection> = new Map()
  private channels: Map<string, Set<string>> = new Map()
  private messageQueue: Map<string, SSEMessage[]> = new Map()

  constructor() {
    this.initializeChannels()
    this.startCleanupTimer()
  }

  /**
   * Initialize default channels
   */
  private initializeChannels(): void {
    const defaultChannels = ['system', 'stats', 'notifications', 'dashboard']
    defaultChannels.forEach(channel => {
      this.channels.set(channel, new Set())
    })
  }

  /**
   * Handle SSE connection
   */
  handleConnection(request: any, response: any): string {
    const connectionId = this.generateConnectionId()
    const clientIP = request.socket?.remoteAddress || 'unknown'
    const userAgent = request.headers['user-agent'] || 'unknown'

    // Set SSE headers
    response.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    })

    const connection: SSEConnection = {
      id: connectionId,
      response,
      channels: new Set(),
      lastActivity: new Date(),
      metadata: {
        ip: clientIP,
        userAgent,
        connectedAt: new Date()
      }
    }

    this.connections.set(connectionId, connection)

    // Send initial connection message
    this.sendToConnection(connectionId, {
      id: this.generateMessageId(),
      event: 'connected',
      data: {
        connectionId,
        message: 'Connected to Kilat.js SSE server',
        timestamp: new Date()
      }
    })

    // Handle client disconnect
    request.on('close', () => {
      this.handleDisconnection(connectionId)
    })

    console.log(`📡 New SSE connection: ${connectionId} (${this.connections.size} total)`)
    return connectionId
  }

  /**
   * Subscribe connection to channel
   */
  subscribe(connectionId: string, channelName: string): boolean {
    const connection = this.connections.get(connectionId)
    if (!connection) return false

    if (!this.channels.has(channelName)) {
      this.channels.set(channelName, new Set())
    }

    connection.channels.add(channelName)
    this.channels.get(channelName)!.add(connectionId)

    this.sendToConnection(connectionId, {
      id: this.generateMessageId(),
      event: 'subscribed',
      data: {
        channel: channelName,
        message: `Subscribed to ${channelName}`,
        subscriberCount: this.channels.get(channelName)!.size
      }
    })

    // Send queued messages for this channel
    const queuedMessages = this.messageQueue.get(channelName) || []
    queuedMessages.slice(-10).forEach(message => {
      this.sendToConnection(connectionId, message)
    })

    console.log(`📺 ${connectionId} subscribed to ${channelName} via SSE`)
    return true
  }

  /**
   * Unsubscribe connection from channel
   */
  unsubscribe(connectionId: string, channelName: string): boolean {
    const connection = this.connections.get(connectionId)
    if (!connection) return false

    connection.channels.delete(channelName)
    this.channels.get(channelName)?.delete(connectionId)

    this.sendToConnection(connectionId, {
      id: this.generateMessageId(),
      event: 'unsubscribed',
      data: {
        channel: channelName,
        message: `Unsubscribed from ${channelName}`
      }
    })

    console.log(`📺 ${connectionId} unsubscribed from ${channelName} via SSE`)
    return true
  }

  /**
   * Broadcast message to channel
   */
  broadcast(channelName: string, data: any, event?: string): void {
    const message: SSEMessage = {
      id: this.generateMessageId(),
      event: event || 'message',
      data,
      retry: 3000
    }

    const subscribers = this.channels.get(channelName)
    if (!subscribers) return

    subscribers.forEach(connectionId => {
      this.sendToConnection(connectionId, message)
    })

    // Queue message for new subscribers
    if (!this.messageQueue.has(channelName)) {
      this.messageQueue.set(channelName, [])
    }

    const queue = this.messageQueue.get(channelName)!
    queue.push(message)

    // Keep only last 50 messages per channel
    if (queue.length > 50) {
      queue.splice(0, queue.length - 50)
    }

    console.log(`📡 Broadcasted to ${channelName}: ${subscribers.size} subscribers`)
  }

  /**
   * Send message to specific connection
   */
  private sendToConnection(connectionId: string, message: SSEMessage): void {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    try {
      const formattedMessage = this.formatSSEMessage(message)
      connection.response.write(formattedMessage)
      connection.lastActivity = new Date()
    } catch (error) {
      console.error(`Error sending SSE message to ${connectionId}:`, error)
      this.handleDisconnection(connectionId)
    }
  }

  /**
   * Format message for SSE
   */
  private formatSSEMessage(message: SSEMessage): string {
    let formatted = ''
    
    if (message.id) {
      formatted += `id: ${message.id}\n`
    }
    
    if (message.event) {
      formatted += `event: ${message.event}\n`
    }
    
    if (message.retry) {
      formatted += `retry: ${message.retry}\n`
    }
    
    const dataString = typeof message.data === 'string' 
      ? message.data 
      : JSON.stringify(message.data)
    
    // Split data by lines for proper SSE format
    dataString.split('\n').forEach(line => {
      formatted += `data: ${line}\n`
    })
    
    formatted += '\n'
    return formatted
  }

  /**
   * Handle disconnection
   */
  private handleDisconnection(connectionId: string): void {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    // Remove from all channels
    connection.channels.forEach(channelName => {
      this.channels.get(channelName)?.delete(connectionId)
    })

    this.connections.delete(connectionId)
    console.log(`🔌 SSE disconnected: ${connectionId} (${this.connections.size} remaining)`)
  }

  /**
   * Start cleanup timer
   */
  private startCleanupTimer(): void {
    setInterval(() => {
      const now = Date.now()
      const timeout = 5 * 60 * 1000 // 5 minutes

      this.connections.forEach((connection, connectionId) => {
        if (now - connection.lastActivity.getTime() > timeout) {
          console.log(`🧹 Cleaning up inactive SSE connection: ${connectionId}`)
          this.handleDisconnection(connectionId)
        }
      })
    }, 60000) // Check every minute
  }

  /**
   * Generate connection ID
   */
  private generateConnectionId(): string {
    return `sse_${Date.now()}_${randomBytes(8).toString('hex')}`
  }

  /**
   * Generate message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${randomBytes(4).toString('hex')}`
  }

  /**
   * Get server statistics
   */
  getStats(): any {
    return {
      connections: this.connections.size,
      channels: this.channels.size,
      totalMessages: Array.from(this.messageQueue.values())
        .reduce((sum, queue) => sum + queue.length, 0),
      channelStats: Array.from(this.channels.entries()).map(([name, subscribers]) => ({
        name,
        subscribers: subscribers.size,
        queuedMessages: this.messageQueue.get(name)?.length || 0
      }))
    }
  }

  /**
   * Send system stats to all subscribers
   */
  broadcastSystemStats(): void {
    const stats = {
      timestamp: new Date(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      connections: this.connections.size,
      channels: this.channels.size
    }

    this.broadcast('stats', stats, 'system-stats')
  }

  /**
   * Send notification to all subscribers
   */
  sendNotification(message: string, type: 'info' | 'warning' | 'error' = 'info'): void {
    const notification = {
      id: this.generateMessageId(),
      type,
      message,
      timestamp: new Date()
    }

    this.broadcast('notifications', notification, 'notification')
  }

  /**
   * Stop server
   */
  stop(): void {
    this.connections.forEach((connection, connectionId) => {
      this.sendToConnection(connectionId, {
        id: this.generateMessageId(),
        event: 'server-shutdown',
        data: { message: 'Server is shutting down' }
      })
      
      connection.response.end()
    })

    this.connections.clear()
    this.channels.clear()
    this.messageQueue.clear()
    
    console.log('📡 SSE server stopped')
  }
}
