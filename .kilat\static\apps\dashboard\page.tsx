import { KilatCard } from '../components/ui/card' export default function DashboardPage() { return ( React.createElement('div', null, <div className="animate-kilat-fade-in"> <h1 className="text-3xl font-bold mb-2">Dashboard Overview</h1> <p className="text-glow-muted">Welcome to your Kilat.js dashboard</p> ) {} React.createElement('div', null, <KilatCard className="animate-kilat-slide-up"> <div className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm text-glow-muted">Total Users</p> <p className="text-2xl font-bold text-glow-primary">1,234</p> ) React.createElement('div', null, 👥 ) </div> </div> </KilatCard> React.createElement('KilatCard', null, <div className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm text-glow-muted">Active Sessions</p> <p className="text-2xl font-bold text-glow-secondary">567</p> </div> <div className="w-12 h-12 bg-glow-secondary/20 rounded-lg flex items-center justify-center"> 🔥 </div> </div> </div> ) React.createElement('KilatCard', null, <div className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm text-glow-muted">API Requests</p> <p className="text-2xl font-bold text-glow-accent">89.2K</p> </div> <div className="w-12 h-12 bg-glow-accent/20 rounded-lg flex items-center justify-center"> 📡 </div> </div> </div> ) React.createElement('KilatCard', null, <div className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm text-glow-muted">Performance</p> <p className="text-2xl font-bold text-green-400">99.9%</p> </div> <div className="w-12 h-12 bg-green-400/20 rounded-lg flex items-center justify-center"> ⚡ </div> </div> </div> ) </div> {} React.createElement('div', null, <KilatCard className="animate-kilat-scale-in"> <div className="p-6"> <h2 className="text-xl font-semibold mb-4">Recent Activity</h2> <div className="space-y-4"> <div className="flex items-center space-x-3"> <div className="w-2 h-2 bg-glow-primary rounded-full">) React.createElement('span', null, New user registered) React.createElement('span', null, 2 min ago) </div> React.createElement('div', null, <div className="w-2 h-2 bg-glow-secondary rounded-full">) React.createElement('span', null, API endpoint updated) React.createElement('span', null, 5 min ago) </div> React.createElement('div', null, <div className="w-2 h-2 bg-glow-accent rounded-full">) React.createElement('span', null, Database backup completed) React.createElement('span', null, 1 hour ago) </div> </div> </div> </KilatCard> React.createElement('KilatCard', null, <div className="p-6"> <h2 className="text-xl font-semibold mb-4">System Status</h2> <div className="space-y-4"> <div className="flex items-center justify-between"> <span className="text-sm">SpeedRun Runtime</span> <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">Online</span> </div> <div className="flex items-center justify-between"> <span className="text-sm">KilatPack Build</span> <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">Ready</span> </div> <div className="flex items-center justify-between"> <span className="text-sm">Database</span> <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">Connected</span> </div> <div className="flex items-center justify-between"> <span className="text-sm">Cache Layer</span> <span className="text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded">Warming</span> </div> </div> </div> ) </div> </div> ) }
//# sourceMappingURL=apps\dashboard\page.tsx.map