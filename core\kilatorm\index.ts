/**
 * KilatORM - Lightweight ORM for Kilat.js
 * Simple and fast database abstraction layer with real SQLite support
 */

import { DatabaseConnection } from '../kilatcore/types'
import { join } from 'path'
import { existsSync, mkdirSync } from 'fs'

// In-memory database for demo purposes
class InMemoryDatabase {
  private data: Map<string, any[]> = new Map()

  constructor() {
    this.initializeData()
  }

  private initializeData() {
    const now = new Date().toISOString()

    // Initialize users
    this.data.set('users', [
      {
        id: 'user_admin',
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'admin',
        passwordHash: 'hashed_admin_password',
        avatar: '/avatars/admin.jpg',
        status: 'active',
        lastLoginAt: now,
        createdAt: now,
        updatedAt: now
      },
      {
        id: 'user_john',
        email: '<EMAIL>',
        name: '<PERSON>',
        role: 'user',
        passwordHash: 'hashed_john_password',
        avatar: '/avatars/john.jpg',
        status: 'active',
        lastLoginAt: now,
        createdAt: now,
        updatedAt: now
      },
      {
        id: 'user_jane',
        email: '<EMAIL>',
        name: '<PERSON>',
        role: 'user',
        passwordHash: 'hashed_jane_password',
        avatar: '/avatars/jane.jpg',
        status: 'active',
        lastLoginAt: now,
        createdAt: now,
        updatedAt: now
      }
    ])

    // Initialize products
    this.data.set('products', [
      {
        id: 'prod_laptop',
        name: 'Gaming Laptop',
        description: 'High-performance gaming laptop with RTX 4080',
        price: 1299.99,
        category: 'Electronics',
        stock: 15,
        image: '/products/laptop.jpg',
        status: 'active',
        createdAt: now,
        updatedAt: now
      },
      {
        id: 'prod_phone',
        name: 'Smartphone Pro',
        description: 'Latest flagship smartphone with AI camera',
        price: 899.99,
        category: 'Electronics',
        stock: 25,
        image: '/products/phone.jpg',
        status: 'active',
        createdAt: now,
        updatedAt: now
      },
      {
        id: 'prod_headphones',
        name: 'Wireless Headphones',
        description: 'Premium noise-cancelling wireless headphones',
        price: 299.99,
        category: 'Audio',
        stock: 30,
        image: '/products/headphones.jpg',
        status: 'active',
        createdAt: now,
        updatedAt: now
      }
    ])

    // Initialize orders
    this.data.set('orders', [
      {
        id: 'order_1',
        userId: 'user_john',
        total: 1599.98,
        status: 'completed',
        items: JSON.stringify([
          { productId: 'prod_laptop', quantity: 1, price: 1299.99 },
          { productId: 'prod_headphones', quantity: 1, price: 299.99 }
        ]),
        shippingAddress: JSON.stringify({
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zip: '10001'
        }),
        createdAt: now,
        updatedAt: now
      },
      {
        id: 'order_2',
        userId: 'user_jane',
        total: 899.99,
        status: 'pending',
        items: JSON.stringify([
          { productId: 'prod_phone', quantity: 1, price: 899.99 }
        ]),
        shippingAddress: JSON.stringify({
          street: '456 Oak Ave',
          city: 'Los Angeles',
          state: 'CA',
          zip: '90210'
        }),
        createdAt: now,
        updatedAt: now
      }
    ])
  }

  exec(sql: string) {
    // Mock exec for table creation
    console.log('Mock EXEC:', sql)
  }

  prepare(sql: string) {
    return {
      all: (...params: any[]) => this.executeQuery(sql, params),
      run: (...params: any[]) => this.executeQuery(sql, params),
      get: (...params: any[]) => {
        const result = this.executeQuery(sql, params)
        return Array.isArray(result) ? result[0] : result
      }
    }
  }

  private executeQuery(sql: string, params: any[] = []) {
    console.log('Mock Query:', sql, params)

    const sqlUpper = sql.toUpperCase().trim()

    if (sqlUpper.includes('SELECT COUNT(*)')) {
      return [{ count: 3 }]
    }

    if (sqlUpper.startsWith('SELECT')) {
      // Extract table name
      const tableMatch = sql.match(/FROM\s+(\w+)/i)
      if (tableMatch) {
        const tableName = tableMatch[1]
        return this.data.get(tableName) || []
      }
    }

    if (sqlUpper.startsWith('INSERT')) {
      return { changes: 1, lastInsertRowid: Date.now() }
    }

    if (sqlUpper.startsWith('UPDATE') || sqlUpper.startsWith('DELETE')) {
      return { changes: 1 }
    }

    return []
  }

  close() {
    console.log('Mock database closed')
  }
}

interface QueryOptions {
  limit?: number
  skip?: number
  sort?: Record<string, 1 | -1>
  select?: string[]
}

interface ModelDefinition {
  tableName: string
  schema: Record<string, any>
  timestamps?: boolean
}

export class KilatORM {
  private connection: DatabaseConnection | null = null
  private models: Map<string, Model> = new Map()

  constructor(dbPath?: string) {
    // Initialize with real SQLite connection
    this.connection = new SQLiteConnection(dbPath)
  }
  
  /**
   * Connect to database
   */
  async connect(connectionString?: string): Promise<void> {
    if (this.connection) {
      await this.connection.connect()
      console.log('📊 KilatORM: Connected to database')
    }
  }
  
  /**
   * Disconnect from database
   */
  async disconnect(): Promise<void> {
    if (this.connection) {
      await this.connection.disconnect()
      console.log('📊 KilatORM: Disconnected from database')
    }
  }
  
  /**
   * Define a model
   */
  model(name: string, definition: ModelDefinition): Model {
    const model = new Model(name, definition, this.connection!)
    this.models.set(name, model)
    return model
  }
  
  /**
   * Get a model
   */
  getModel(name: string): Model | undefined {
    return this.models.get(name)
  }
  
  /**
   * Get users model (convenience method)
   */
  get users(): Model {
    if (!this.models.has('users')) {
      this.model('users', {
        tableName: 'users',
        schema: {
          id: { type: 'string', primary: true },
          email: { type: 'string', unique: true },
          name: { type: 'string' },
          role: { type: 'string', default: 'user' },
          passwordHash: { type: 'string' },
          avatar: { type: 'string', nullable: true },
          status: { type: 'string', default: 'active' },
          lastLoginAt: { type: 'date', nullable: true },
          createdAt: { type: 'date' },
          updatedAt: { type: 'date' },
        },
        timestamps: true,
      })
    }
    return this.models.get('users')!
  }

  /**
   * Get products model (convenience method)
   */
  get products(): Model {
    if (!this.models.has('products')) {
      this.model('products', {
        tableName: 'products',
        schema: {
          id: { type: 'string', primary: true },
          name: { type: 'string' },
          description: { type: 'text' },
          price: { type: 'number' },
          category: { type: 'string' },
          stock: { type: 'number', default: 0 },
          image: { type: 'string', nullable: true },
          status: { type: 'string', default: 'active' },
          createdAt: { type: 'date' },
          updatedAt: { type: 'date' },
        },
        timestamps: true,
      })
    }
    return this.models.get('products')!
  }

  /**
   * Get orders model (convenience method)
   */
  get orders(): Model {
    if (!this.models.has('orders')) {
      this.model('orders', {
        tableName: 'orders',
        schema: {
          id: { type: 'string', primary: true },
          userId: { type: 'string' },
          total: { type: 'number' },
          status: { type: 'string', default: 'pending' },
          items: { type: 'json' },
          shippingAddress: { type: 'json' },
          createdAt: { type: 'date' },
          updatedAt: { type: 'date' },
        },
        timestamps: true,
      })
    }
    return this.models.get('orders')!
  }
}

class QueryBuilder {
  private model: Model
  private query: Record<string, any> = {}
  private options: QueryOptions = {}

  constructor(model: Model, query: Record<string, any> = {}) {
    this.model = model
    this.query = query
  }

  limit(count: number): QueryBuilder {
    this.options.limit = count
    return this
  }

  skip(count: number): QueryBuilder {
    this.options.skip = count
    return this
  }

  sort(sortOptions: Record<string, 1 | -1>): QueryBuilder {
    this.options.sort = sortOptions
    return this
  }

  async exec(): Promise<any[]> {
    return this.model.executeFind(this.query, this.options)
  }

  // Make it thenable so it can be awaited directly
  then(resolve: (value: any[]) => any, reject?: (reason: any) => any) {
    return this.exec().then(resolve, reject)
  }
}

export class Model {
  private name: string
  private definition: ModelDefinition
  private connection: DatabaseConnection

  constructor(name: string, definition: ModelDefinition, connection: DatabaseConnection) {
    this.name = name
    this.definition = definition
    this.connection = connection
  }

  /**
   * Find multiple documents - returns QueryBuilder for chaining
   */
  find(query: Record<string, any> = {}): QueryBuilder {
    return new QueryBuilder(this, query)
  }

  /**
   * Execute find query (used by QueryBuilder)
   */
  async executeFind(query: Record<string, any> = {}, options: QueryOptions = {}): Promise<any[]> {
    const sql = this.buildSelectQuery(query, options)
    const result = await this.connection.query(sql)
    return result.rows || []
  }
  
  /**
   * Find one document
   */
  async findOne(query: Record<string, any>): Promise<any | null> {
    const results = await this.find(query).limit(1)
    return results[0] || null
  }
  
  /**
   * Find by ID
   */
  async findById(id: string): Promise<any | null> {
    return this.findOne({ id })
  }
  
  /**
   * Create a new document
   */
  async create(data: Record<string, any>): Promise<any> {
    if (this.definition.timestamps) {
      data.createdAt = new Date()
      data.updatedAt = new Date()
    }
    
    const sql = this.buildInsertQuery(data)
    await this.connection.query(sql)
    return data
  }
  
  /**
   * Update documents
   */
  async updateOne(query: Record<string, any>, update: Record<string, any>): Promise<any> {
    if (this.definition.timestamps) {
      update.updatedAt = new Date()
    }
    
    const sql = this.buildUpdateQuery(query, update)
    await this.connection.query(sql)
    
    // Return updated document
    return this.findOne(query)
  }
  
  /**
   * Update multiple documents
   */
  async updateMany(query: Record<string, any>, update: Record<string, any>): Promise<number> {
    if (this.definition.timestamps) {
      update.updatedAt = new Date()
    }
    
    const sql = this.buildUpdateQuery(query, update)
    const result = await this.connection.query(sql)
    return result.affectedRows || 0
  }
  
  /**
   * Delete one document
   */
  async deleteOne(query: Record<string, any>): Promise<boolean> {
    const sql = this.buildDeleteQuery(query, true)
    const result = await this.connection.query(sql)
    return (result.affectedRows || 0) > 0
  }
  
  /**
   * Delete multiple documents
   */
  async deleteMany(query: Record<string, any>): Promise<number> {
    const sql = this.buildDeleteQuery(query, false)
    const result = await this.connection.query(sql)
    return result.affectedRows || 0
  }
  
  /**
   * Count documents
   */
  async countDocuments(query: Record<string, any> = {}): Promise<number> {
    const sql = this.buildCountQuery(query)
    const result = await this.connection.query(sql)
    return result.rows?.[0]?.count || 0
  }
  
  /**
   * Aggregate data
   */
  async aggregate(pipeline: any[]): Promise<any[]> {
    // Simple aggregation implementation
    // In a real implementation, this would handle complex aggregation pipelines
    return []
  }
  
  /**
   * Build SELECT query
   */
  private buildSelectQuery(query: Record<string, any>, options: QueryOptions): string {
    const tableName = this.definition.tableName
    const whereClause = this.buildWhereClause(query)
    const selectClause = options.select ? options.select.join(', ') : '*'
    
    let sql = `SELECT ${selectClause} FROM ${tableName}`
    
    if (whereClause) {
      sql += ` WHERE ${whereClause}`
    }
    
    if (options.sort) {
      const sortKeys = Object.keys(options.sort)
      const orderBy = sortKeys
        .map((field: string) => `${field} ${options.sort![field] === 1 ? 'ASC' : 'DESC'}`)
        .join(', ')
      sql += ` ORDER BY ${orderBy}`
    }
    
    if (options.limit) {
      sql += ` LIMIT ${options.limit}`
    }
    
    if (options.skip) {
      sql += ` OFFSET ${options.skip}`
    }
    
    return sql
  }
  
  /**
   * Build INSERT query
   */
  private buildInsertQuery(data: Record<string, any>): string {
    const tableName = this.definition.tableName
    const fields = Object.keys(data).join(', ')
    const values = Object.keys(data).map(key => `'${data[key]}'`).join(', ')

    return `INSERT INTO ${tableName} (${fields}) VALUES (${values})`
  }
  
  /**
   * Build UPDATE query
   */
  private buildUpdateQuery(query: Record<string, any>, update: Record<string, any>): string {
    const tableName = this.definition.tableName
    const setClause = Object.keys(update)
      .map((key: string) => `${key} = '${update[key]}'`)
      .join(', ')
    const whereClause = this.buildWhereClause(query)
    
    let sql = `UPDATE ${tableName} SET ${setClause}`
    
    if (whereClause) {
      sql += ` WHERE ${whereClause}`
    }
    
    return sql
  }
  
  /**
   * Build DELETE query
   */
  private buildDeleteQuery(query: Record<string, any>, limitOne: boolean): string {
    const tableName = this.definition.tableName
    const whereClause = this.buildWhereClause(query)
    
    let sql = `DELETE FROM ${tableName}`
    
    if (whereClause) {
      sql += ` WHERE ${whereClause}`
    }
    
    if (limitOne) {
      sql += ` LIMIT 1`
    }
    
    return sql
  }
  
  /**
   * Build COUNT query
   */
  private buildCountQuery(query: Record<string, any>): string {
    const tableName = this.definition.tableName
    const whereClause = this.buildWhereClause(query)
    
    let sql = `SELECT COUNT(*) as count FROM ${tableName}`
    
    if (whereClause) {
      sql += ` WHERE ${whereClause}`
    }
    
    return sql
  }
  
  /**
   * Build WHERE clause
   */
  private buildWhereClause(query: Record<string, any>): string {
    if (Object.keys(query).length === 0) {
      return ''
    }
    
    const conditions = Object.entries(query).map(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        // Handle operators like $regex, $gt, $lt, etc.
        if (value.$regex) {
          return `${key} LIKE '%${value.$regex}%'`
        }
        if (value.$gte) {
          return `${key} >= '${value.$gte}'`
        }
        if (value.$lte) {
          return `${key} <= '${value.$lte}'`
        }
        // Add more operators as needed
      }
      
      return `${key} = '${value}'`
    })
    
    return conditions.join(' AND ')
  }
}

/**
 * Real SQLite database connection
 */
class SQLiteConnection implements DatabaseConnection {
  private db: any = null
  private dbPath: string

  constructor(dbPath?: string) {
    this.dbPath = dbPath || join('.', '.kilat', 'database.sqlite')
  }

  async connect(): Promise<void> {
    try {
      // Ensure database directory exists
      const dbDir = join('.', '.kilat')
      if (!existsSync(dbDir)) {
        mkdirSync(dbDir, { recursive: true })
      }

      // Connect to in-memory database for demo
      this.db = new InMemoryDatabase()

      // Enable foreign keys
      this.db.exec('PRAGMA foreign_keys = ON')

      // Create tables if they don't exist
      await this.createTables()

      // Seed initial data
      await this.seedData()

      console.log('📊 KilatORM: Connected to SQLite database at', this.dbPath)
    } catch (error) {
      console.error('Failed to connect to database:', error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    if (this.db) {
      this.db.close()
      this.db = null
      console.log('📊 KilatORM: Disconnected from database')
    }
  }

  async query(sql: string, params?: any[]): Promise<any> {
    if (!this.db) {
      throw new Error('Database not connected')
    }

    try {
      console.log('🔍 SQL Query:', sql, params ? `Params: ${JSON.stringify(params)}` : '')

      if (sql.trim().toUpperCase().startsWith('SELECT')) {
        const stmt = this.db.prepare(sql)
        const rows = params ? stmt.all(...params) : stmt.all()
        return { rows }
      } else {
        const stmt = this.db.prepare(sql)
        const result = params ? stmt.run(...params) : stmt.run()
        return {
          affectedRows: result.changes,
          insertId: result.lastInsertRowid
        }
      }
    } catch (error) {
      console.error('Database query error:', error)
      throw error
    }
  }

  async transaction(callback: (tx: any) => Promise<any>): Promise<any> {
    if (!this.db) {
      throw new Error('Database not connected')
    }

    const transaction = this.db.transaction(callback)
    return transaction()
  }

  private async createTables(): Promise<void> {
    if (!this.db) return

    // Users table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        role TEXT DEFAULT 'user',
        passwordHash TEXT NOT NULL,
        avatar TEXT,
        status TEXT DEFAULT 'active',
        lastLoginAt DATETIME,
        createdAt DATETIME NOT NULL,
        updatedAt DATETIME NOT NULL
      )
    `)

    // Products table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS products (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        price REAL NOT NULL,
        category TEXT NOT NULL,
        stock INTEGER DEFAULT 0,
        image TEXT,
        status TEXT DEFAULT 'active',
        createdAt DATETIME NOT NULL,
        updatedAt DATETIME NOT NULL
      )
    `)

    // Orders table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS orders (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        total REAL NOT NULL,
        status TEXT DEFAULT 'pending',
        items TEXT NOT NULL,
        shippingAddress TEXT,
        createdAt DATETIME NOT NULL,
        updatedAt DATETIME NOT NULL,
        FOREIGN KEY (userId) REFERENCES users (id)
      )
    `)

    console.log('📊 Database tables created/verified')
  }

  private async seedData(): Promise<void> {
    if (!this.db) return

    // Check if data already exists
    const userCount = this.db.prepare('SELECT COUNT(*) as count FROM users').get() as { count: number }

    if (userCount.count === 0) {
      const now = new Date().toISOString()

      // Seed users
      const users = [
        {
          id: 'user_admin',
          email: '<EMAIL>',
          name: 'Admin User',
          role: 'admin',
          passwordHash: 'hashed_admin_password',
          avatar: '/avatars/admin.jpg',
          status: 'active',
          lastLoginAt: now,
          createdAt: now,
          updatedAt: now
        },
        {
          id: 'user_john',
          email: '<EMAIL>',
          name: 'John Doe',
          role: 'user',
          passwordHash: 'hashed_john_password',
          avatar: '/avatars/john.jpg',
          status: 'active',
          lastLoginAt: now,
          createdAt: now,
          updatedAt: now
        },
        {
          id: 'user_jane',
          email: '<EMAIL>',
          name: 'Jane Smith',
          role: 'user',
          passwordHash: 'hashed_jane_password',
          avatar: '/avatars/jane.jpg',
          status: 'active',
          lastLoginAt: now,
          createdAt: now,
          updatedAt: now
        }
      ]

      const insertUser = this.db.prepare(`
        INSERT INTO users (id, email, name, role, passwordHash, avatar, status, lastLoginAt, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      for (const user of users) {
        insertUser.run(
          user.id, user.email, user.name, user.role, user.passwordHash,
          user.avatar, user.status, user.lastLoginAt, user.createdAt, user.updatedAt
        )
      }

      // Seed products
      const products = [
        {
          id: 'prod_laptop',
          name: 'Gaming Laptop',
          description: 'High-performance gaming laptop with RTX 4080',
          price: 1299.99,
          category: 'Electronics',
          stock: 15,
          image: '/products/laptop.jpg',
          status: 'active',
          createdAt: now,
          updatedAt: now
        },
        {
          id: 'prod_phone',
          name: 'Smartphone Pro',
          description: 'Latest flagship smartphone with AI camera',
          price: 899.99,
          category: 'Electronics',
          stock: 25,
          image: '/products/phone.jpg',
          status: 'active',
          createdAt: now,
          updatedAt: now
        },
        {
          id: 'prod_headphones',
          name: 'Wireless Headphones',
          description: 'Premium noise-cancelling wireless headphones',
          price: 299.99,
          category: 'Audio',
          stock: 30,
          image: '/products/headphones.jpg',
          status: 'active',
          createdAt: now,
          updatedAt: now
        }
      ]

      const insertProduct = this.db.prepare(`
        INSERT INTO products (id, name, description, price, category, stock, image, status, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      for (const product of products) {
        insertProduct.run(
          product.id, product.name, product.description, product.price, product.category,
          product.stock, product.image, product.status, product.createdAt, product.updatedAt
        )
      }

      // Seed orders
      const orders = [
        {
          id: 'order_1',
          userId: 'user_john',
          total: 1599.98,
          status: 'completed',
          items: JSON.stringify([
            { productId: 'prod_laptop', quantity: 1, price: 1299.99 },
            { productId: 'prod_headphones', quantity: 1, price: 299.99 }
          ]),
          shippingAddress: JSON.stringify({
            street: '123 Main St',
            city: 'New York',
            state: 'NY',
            zip: '10001'
          }),
          createdAt: now,
          updatedAt: now
        },
        {
          id: 'order_2',
          userId: 'user_jane',
          total: 899.99,
          status: 'pending',
          items: JSON.stringify([
            { productId: 'prod_phone', quantity: 1, price: 899.99 }
          ]),
          shippingAddress: JSON.stringify({
            street: '456 Oak Ave',
            city: 'Los Angeles',
            state: 'CA',
            zip: '90210'
          }),
          createdAt: now,
          updatedAt: now
        }
      ]

      const insertOrder = this.db.prepare(`
        INSERT INTO orders (id, userId, total, status, items, shippingAddress, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `)

      for (const order of orders) {
        insertOrder.run(
          order.id, order.userId, order.total, order.status,
          order.items, order.shippingAddress, order.createdAt, order.updatedAt
        )
      }

      console.log('📊 Database seeded with initial data')
    }
  }
}
