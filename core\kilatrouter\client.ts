/**
 * Ki<PERSON>Router - Client-side Router
 * Standalone routing system without Next.js dependency
 */

export interface Route {
  path: string
  component: () => any
  exact?: boolean
  middleware?: string[]
}

export interface RouterConfig {
  routes: Route[]
  basePath?: string
  hashRouting?: boolean
  scrollToTop?: boolean
}

export class KilatClientRouter {
  private routes: Route[] = []
  private currentPath: string = ''
  private basePath: string = ''
  private hashRouting: boolean = false
  private scrollToTop: boolean = true
  private listeners: Set<(path: string) => void> = new Set()

  constructor(config: RouterConfig) {
    this.routes = config.routes
    this.basePath = config.basePath || ''
    this.hashRouting = config.hashRouting || false
    this.scrollToTop = config.scrollToTop !== false

    this.init()
  }

  /**
   * Initialize router
   */
  private init(): void {
    // Listen for browser navigation
    window.addEventListener('popstate', this.handlePopState.bind(this))
    
    // Handle initial route
    this.handleRoute(this.getCurrentPath())
  }

  /**
   * Get current path
   */
  private getCurrentPath(): string {
    if (this.hashRouting) {
      return window.location.hash.slice(1) || '/'
    }
    return window.location.pathname
  }

  /**
   * Handle popstate event
   */
  private handlePopState(): void {
    const path = this.getCurrentPath()
    this.handleRoute(path)
  }

  /**
   * Navigate to path
   */
  navigate(path: string, replace: boolean = false): void {
    const fullPath = this.basePath + path

    if (this.hashRouting) {
      if (replace) {
        window.location.replace(`#${path}`)
      } else {
        window.location.hash = path
      }
    } else {
      if (replace) {
        window.history.replaceState({}, '', fullPath)
      } else {
        window.history.pushState({}, '', fullPath)
      }
    }

    this.handleRoute(path)
  }

  /**
   * Handle route change
   */
  private handleRoute(path: string): void {
    this.currentPath = path
    
    // Scroll to top if enabled
    if (this.scrollToTop) {
      window.scrollTo(0, 0)
    }

    // Notify listeners
    this.listeners.forEach(listener => listener(path))

    // Find and render matching route
    const route = this.findRoute(path)
    if (route) {
      this.renderRoute(route)
    } else {
      this.render404()
    }
  }

  /**
   * Find matching route
   */
  private findRoute(path: string): Route | null {
    for (const route of this.routes) {
      if (this.matchRoute(route, path)) {
        return route
      }
    }
    return null
  }

  /**
   * Check if route matches path
   */
  private matchRoute(route: Route, path: string): boolean {
    if (route.exact) {
      return route.path === path
    }

    // Simple pattern matching
    const routeParts = route.path.split('/')
    const pathParts = path.split('/')

    if (routeParts.length > pathParts.length) {
      return false
    }

    for (let i = 0; i < routeParts.length; i++) {
      const routePart = routeParts[i]
      const pathPart = pathParts[i]

      if (routePart.startsWith(':')) {
        // Dynamic parameter
        continue
      }

      if (routePart !== pathPart) {
        return false
      }
    }

    return true
  }

  /**
   * Extract parameters from path
   */
  extractParams(route: Route, path: string): Record<string, string> {
    const params: Record<string, string> = {}
    const routeParts = route.path.split('/')
    const pathParts = path.split('/')

    for (let i = 0; i < routeParts.length; i++) {
      const routePart = routeParts[i]
      const pathPart = pathParts[i]

      if (routePart.startsWith(':')) {
        const paramName = routePart.slice(1)
        params[paramName] = pathPart
      }
    }

    return params
  }

  /**
   * Render route component
   */
  private renderRoute(route: Route): void {
    try {
      const params = this.extractParams(route, this.currentPath)
      const component = route.component()
      
      // In a real implementation, this would render the component
      // For now, we'll just log it
      console.log('Rendering route:', route.path, 'with params:', params)
      
      // Trigger custom event for component rendering
      window.dispatchEvent(new CustomEvent('kilat:route-change', {
        detail: { route, params, path: this.currentPath }
      }))
      
    } catch (error) {
      console.error('Error rendering route:', error)
      this.render404()
    }
  }

  /**
   * Render 404 page
   */
  private render404(): void {
    console.log('404 - Page not found:', this.currentPath)
    
    window.dispatchEvent(new CustomEvent('kilat:404', {
      detail: { path: this.currentPath }
    }))
  }

  /**
   * Add route listener
   */
  onRouteChange(listener: (path: string) => void): () => void {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  /**
   * Get current route
   */
  getCurrentRoute(): string {
    return this.currentPath
  }

  /**
   * Check if path is current
   */
  isActive(path: string): boolean {
    return this.currentPath === path
  }

  /**
   * Go back
   */
  back(): void {
    window.history.back()
  }

  /**
   * Go forward
   */
  forward(): void {
    window.history.forward()
  }

  /**
   * Refresh current route
   */
  refresh(): void {
    this.handleRoute(this.currentPath)
  }
}

/**
 * Link component for navigation
 */
export interface LinkProps {
  to: string
  children: any
  className?: string
  replace?: boolean
  onClick?: () => void
}

export function createLink(router: KilatClientRouter) {
  return function Link({ to, children, className = '', replace = false, onClick }: LinkProps) {
    const handleClick = (e: Event) => {
      e.preventDefault()
      
      if (onClick) {
        onClick()
      }
      
      router.navigate(to, replace)
    }

    // In a real implementation, this would return JSX
    // For now, we'll return a simple object representation
    return {
      type: 'a',
      props: {
        href: to,
        className: `${className} ${router.isActive(to) ? 'active' : ''}`,
        onClick: handleClick,
        children
      }
    }
  }
}

/**
 * Route guard for protected routes
 */
export interface RouteGuard {
  canActivate: (path: string, params: Record<string, string>) => boolean | Promise<boolean>
  redirectTo?: string
}

export class ProtectedRoute {
  constructor(
    private router: KilatClientRouter,
    private guards: RouteGuard[]
  ) {}

  async checkAccess(path: string, params: Record<string, string>): Promise<boolean> {
    for (const guard of this.guards) {
      const canActivate = await guard.canActivate(path, params)
      if (!canActivate) {
        if (guard.redirectTo) {
          this.router.navigate(guard.redirectTo, true)
        }
        return false
      }
    }
    return true
  }
}

/**
 * Create router instance
 */
export function createRouter(config: RouterConfig): KilatClientRouter {
  return new KilatClientRouter(config)
}

/**
 * Browser history utilities
 */
export const history = {
  push: (path: string) => window.history.pushState({}, '', path),
  replace: (path: string) => window.history.replaceState({}, '', path),
  back: () => window.history.back(),
  forward: () => window.history.forward(),
  go: (delta: number) => window.history.go(delta)
}
