/**
 * KilatAuth - Role-Based Access Control (RBAC)
 * Advanced permission system with roles and policies
 */

export interface Permission {
  id: string
  name: string
  resource: string
  action: string
  description?: string
}

export interface Role {
  id: string
  name: string
  description?: string
  permissions: string[]
  isSystem?: boolean
}

export interface Policy {
  id: string
  name: string
  effect: 'allow' | 'deny'
  resources: string[]
  actions: string[]
  conditions?: PolicyCondition[]
}

export interface PolicyCondition {
  field: string
  operator: 'eq' | 'ne' | 'in' | 'nin' | 'gt' | 'lt' | 'contains'
  value: any
}

export interface AccessContext {
  userId: string
  userRole: string
  userPermissions: string[]
  resource: string
  action: string
  data?: any
  ip?: string
  userAgent?: string
  timestamp?: Date
}

export class KilatRBAC {
  private permissions: Map<string, Permission> = new Map()
  private roles: Map<string, Role> = new Map()
  private policies: Map<string, Policy> = new Map()

  constructor() {
    this.initializeDefaultPermissions()
    this.initializeDefaultRoles()
  }

  /**
   * Initialize default permissions
   */
  private initializeDefaultPermissions(): void {
    const defaultPermissions: Permission[] = [
      // User permissions
      { id: 'user:read', name: 'Read Users', resource: 'user', action: 'read' },
      { id: 'user:create', name: 'Create Users', resource: 'user', action: 'create' },
      { id: 'user:update', name: 'Update Users', resource: 'user', action: 'update' },
      { id: 'user:delete', name: 'Delete Users', resource: 'user', action: 'delete' },
      
      // Product permissions
      { id: 'product:read', name: 'Read Products', resource: 'product', action: 'read' },
      { id: 'product:create', name: 'Create Products', resource: 'product', action: 'create' },
      { id: 'product:update', name: 'Update Products', resource: 'product', action: 'update' },
      { id: 'product:delete', name: 'Delete Products', resource: 'product', action: 'delete' },
      
      // Order permissions
      { id: 'order:read', name: 'Read Orders', resource: 'order', action: 'read' },
      { id: 'order:create', name: 'Create Orders', resource: 'order', action: 'create' },
      { id: 'order:update', name: 'Update Orders', resource: 'order', action: 'update' },
      { id: 'order:delete', name: 'Delete Orders', resource: 'order', action: 'delete' },
      
      // System permissions
      { id: 'system:admin', name: 'System Admin', resource: 'system', action: 'admin' },
      { id: 'system:config', name: 'System Config', resource: 'system', action: 'config' },
      { id: 'analytics:read', name: 'Read Analytics', resource: 'analytics', action: 'read' },
    ]

    defaultPermissions.forEach(permission => {
      this.permissions.set(permission.id, permission)
    })
  }

  /**
   * Initialize default roles
   */
  private initializeDefaultRoles(): void {
    const defaultRoles: Role[] = [
      {
        id: 'super_admin',
        name: 'Super Administrator',
        description: 'Full system access',
        permissions: Array.from(this.permissions.keys()),
        isSystem: true
      },
      {
        id: 'admin',
        name: 'Administrator',
        description: 'Administrative access',
        permissions: [
          'user:read', 'user:create', 'user:update',
          'product:read', 'product:create', 'product:update', 'product:delete',
          'order:read', 'order:update',
          'analytics:read'
        ]
      },
      {
        id: 'manager',
        name: 'Manager',
        description: 'Management access',
        permissions: [
          'user:read',
          'product:read', 'product:create', 'product:update',
          'order:read', 'order:update',
          'analytics:read'
        ]
      },
      {
        id: 'user',
        name: 'Regular User',
        description: 'Basic user access',
        permissions: [
          'product:read',
          'order:read', 'order:create'
        ]
      },
      {
        id: 'guest',
        name: 'Guest',
        description: 'Limited access',
        permissions: [
          'product:read'
        ]
      }
    ]

    defaultRoles.forEach(role => {
      this.roles.set(role.id, role)
    })
  }

  /**
   * Add permission
   */
  addPermission(permission: Permission): void {
    this.permissions.set(permission.id, permission)
  }

  /**
   * Add role
   */
  addRole(role: Role): void {
    this.roles.set(role.id, role)
  }

  /**
   * Add policy
   */
  addPolicy(policy: Policy): void {
    this.policies.set(policy.id, policy)
  }

  /**
   * Check if user has permission
   */
  hasPermission(userPermissions: string[], requiredPermission: string): boolean {
    return userPermissions.includes(requiredPermission)
  }

  /**
   * Check if user can access resource
   */
  canAccess(context: AccessContext): boolean {
    const { userPermissions, resource, action } = context
    const requiredPermission = `${resource}:${action}`
    
    // Check direct permission
    if (this.hasPermission(userPermissions, requiredPermission)) {
      return true
    }

    // Check wildcard permissions
    const wildcardPermission = `${resource}:*`
    if (this.hasPermission(userPermissions, wildcardPermission)) {
      return true
    }

    // Check super admin
    if (this.hasPermission(userPermissions, 'system:admin')) {
      return true
    }

    // Check policies
    return this.evaluatePolicies(context)
  }

  /**
   * Evaluate policies
   */
  private evaluatePolicies(context: AccessContext): boolean {
    for (const policy of this.policies.values()) {
      if (this.matchesPolicy(policy, context)) {
        return policy.effect === 'allow'
      }
    }
    return false
  }

  /**
   * Check if context matches policy
   */
  private matchesPolicy(policy: Policy, context: AccessContext): boolean {
    // Check resource match
    if (!policy.resources.includes(context.resource) && !policy.resources.includes('*')) {
      return false
    }

    // Check action match
    if (!policy.actions.includes(context.action) && !policy.actions.includes('*')) {
      return false
    }

    // Check conditions
    if (policy.conditions) {
      return this.evaluateConditions(policy.conditions, context)
    }

    return true
  }

  /**
   * Evaluate policy conditions
   */
  private evaluateConditions(conditions: PolicyCondition[], context: AccessContext): boolean {
    return conditions.every(condition => {
      const contextValue = this.getContextValue(condition.field, context)
      return this.evaluateCondition(condition, contextValue)
    })
  }

  /**
   * Get value from context
   */
  private getContextValue(field: string, context: AccessContext): any {
    const fieldMap: Record<string, any> = {
      'user.id': context.userId,
      'user.role': context.userRole,
      'resource': context.resource,
      'action': context.action,
      'ip': context.ip,
      'userAgent': context.userAgent,
      'timestamp': context.timestamp,
      ...context.data
    }
    return fieldMap[field]
  }

  /**
   * Evaluate single condition
   */
  private evaluateCondition(condition: PolicyCondition, value: any): boolean {
    switch (condition.operator) {
      case 'eq':
        return value === condition.value
      case 'ne':
        return value !== condition.value
      case 'in':
        return Array.isArray(condition.value) && condition.value.includes(value)
      case 'nin':
        return Array.isArray(condition.value) && !condition.value.includes(value)
      case 'gt':
        return value > condition.value
      case 'lt':
        return value < condition.value
      case 'contains':
        return typeof value === 'string' && value.includes(condition.value)
      default:
        return false
    }
  }

  /**
   * Get role by ID
   */
  getRole(roleId: string): Role | undefined {
    return this.roles.get(roleId)
  }

  /**
   * Get permission by ID
   */
  getPermission(permissionId: string): Permission | undefined {
    return this.permissions.get(permissionId)
  }

  /**
   * Get all permissions for role
   */
  getRolePermissions(roleId: string): Permission[] {
    const role = this.roles.get(roleId)
    if (!role) return []

    return role.permissions
      .map(permId => this.permissions.get(permId))
      .filter(Boolean) as Permission[]
  }

  /**
   * Check if role exists
   */
  roleExists(roleId: string): boolean {
    return this.roles.has(roleId)
  }

  /**
   * Get all roles
   */
  getAllRoles(): Role[] {
    return Array.from(this.roles.values())
  }

  /**
   * Get all permissions
   */
  getAllPermissions(): Permission[] {
    return Array.from(this.permissions.values())
  }
}
