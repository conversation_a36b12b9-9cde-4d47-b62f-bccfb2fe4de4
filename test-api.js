/**
 * Test API endpoints
 */

import { KilatORM } from './core/kilatorm/index.ts'

async function testORM() {
  console.log('🧪 Testing KilatORM...')
  
  try {
    const orm = new KilatORM()
    await orm.connect()
    
    console.log('✅ ORM connected successfully')
    
    // Test users
    const users = await orm.users.find({}).limit(5)
    console.log('👥 Users:', users)
    
    // Test products
    const products = await orm.products.find({}).limit(5)
    console.log('📦 Products:', products)
    
    // Test orders
    const orders = await orm.orders.find({}).limit(5)
    console.log('🛒 Orders:', orders)
    
    await orm.disconnect()
    console.log('✅ Test completed successfully')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

testORM()
