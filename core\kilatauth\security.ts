/**
 * KilatAuth - Security Middleware & Protection
 * Advanced security features including rate limiting, CSRF, and security headers
 */

import { createHash, randomBytes } from 'crypto'

export interface RateLimitConfig {
  windowMs: number
  maxRequests: number
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
  keyGenerator?: (req: any) => string
}

export interface SecurityConfig {
  rateLimit: RateLimitConfig
  csrf: {
    enabled: boolean
    cookieName: string
    headerName: string
    secret: string
  }
  cors: {
    enabled: boolean
    origin: string | string[]
    credentials: boolean
    methods: string[]
    headers: string[]
  }
  headers: {
    contentSecurityPolicy: string
    xFrameOptions: string
    xContentTypeOptions: string
    referrerPolicy: string
    hsts: boolean
  }
}

export interface RequestInfo {
  ip: string
  userAgent: string
  timestamp: number
  path: string
  method: string
  userId?: string
}

export class KilatSecurity {
  private rateLimitStore: Map<string, { count: number; resetTime: number }> = new Map()
  private csrfTokens: Map<string, { token: string; expires: number }> = new Map()
  private config: SecurityConfig

  constructor(config?: Partial<SecurityConfig>) {
    this.config = {
      rateLimit: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        maxRequests: 100,
        ...config?.rateLimit
      },
      csrf: {
        enabled: true,
        cookieName: '_csrf',
        headerName: 'X-CSRF-Token',
        secret: randomBytes(32).toString('hex'),
        ...config?.csrf
      },
      cors: {
        enabled: true,
        origin: '*',
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        headers: ['Content-Type', 'Authorization', 'X-CSRF-Token'],
        ...config?.cors
      },
      headers: {
        contentSecurityPolicy: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
        xFrameOptions: 'DENY',
        xContentTypeOptions: 'nosniff',
        referrerPolicy: 'strict-origin-when-cross-origin',
        hsts: true,
        ...config?.headers
      }
    }
  }

  /**
   * Rate limiting middleware
   */
  rateLimit(req: any): { allowed: boolean; remaining: number; resetTime: number } {
    const key = this.generateRateLimitKey(req)
    const now = Date.now()
    const windowStart = now - this.config.rateLimit.windowMs

    // Clean expired entries
    this.cleanExpiredRateLimits(windowStart)

    let entry = this.rateLimitStore.get(key)
    
    if (!entry || entry.resetTime <= now) {
      // Create new window
      entry = {
        count: 1,
        resetTime: now + this.config.rateLimit.windowMs
      }
      this.rateLimitStore.set(key, entry)
      
      return {
        allowed: true,
        remaining: this.config.rateLimit.maxRequests - 1,
        resetTime: entry.resetTime
      }
    }

    // Increment counter
    entry.count++
    
    const allowed = entry.count <= this.config.rateLimit.maxRequests
    const remaining = Math.max(0, this.config.rateLimit.maxRequests - entry.count)

    return {
      allowed,
      remaining,
      resetTime: entry.resetTime
    }
  }

  /**
   * Generate rate limit key
   */
  private generateRateLimitKey(req: any): string {
    if (this.config.rateLimit.keyGenerator) {
      return this.config.rateLimit.keyGenerator(req)
    }
    
    // Default: use IP address
    return this.getClientIP(req)
  }

  /**
   * Clean expired rate limit entries
   */
  private cleanExpiredRateLimits(windowStart: number): void {
    for (const [key, entry] of this.rateLimitStore.entries()) {
      if (entry.resetTime <= windowStart) {
        this.rateLimitStore.delete(key)
      }
    }
  }

  /**
   * Generate CSRF token
   */
  generateCSRFToken(sessionId: string): string {
    const token = randomBytes(32).toString('hex')
    const expires = Date.now() + (24 * 60 * 60 * 1000) // 24 hours
    
    this.csrfTokens.set(sessionId, { token, expires })
    return token
  }

  /**
   * Verify CSRF token
   */
  verifyCSRFToken(sessionId: string, token: string): boolean {
    const stored = this.csrfTokens.get(sessionId)
    
    if (!stored || stored.expires < Date.now()) {
      this.csrfTokens.delete(sessionId)
      return false
    }

    return stored.token === token
  }

  /**
   * Get security headers
   */
  getSecurityHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'X-Content-Type-Options': this.config.headers.xContentTypeOptions,
      'X-Frame-Options': this.config.headers.xFrameOptions,
      'Referrer-Policy': this.config.headers.referrerPolicy,
      'Content-Security-Policy': this.config.headers.contentSecurityPolicy,
      'X-XSS-Protection': '1; mode=block',
      'X-DNS-Prefetch-Control': 'off',
      'X-Download-Options': 'noopen',
      'X-Permitted-Cross-Domain-Policies': 'none'
    }

    if (this.config.headers.hsts) {
      headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    }

    return headers
  }

  /**
   * Get CORS headers
   */
  getCORSHeaders(origin?: string): Record<string, string> {
    if (!this.config.cors.enabled) {
      return {}
    }

    const headers: Record<string, string> = {
      'Access-Control-Allow-Methods': this.config.cors.methods.join(', '),
      'Access-Control-Allow-Headers': this.config.cors.headers.join(', '),
      'Access-Control-Max-Age': '86400' // 24 hours
    }

    if (this.config.cors.credentials) {
      headers['Access-Control-Allow-Credentials'] = 'true'
    }

    // Handle origin
    if (Array.isArray(this.config.cors.origin)) {
      if (origin && this.config.cors.origin.includes(origin)) {
        headers['Access-Control-Allow-Origin'] = origin
      }
    } else if (this.config.cors.origin === '*') {
      headers['Access-Control-Allow-Origin'] = '*'
    } else if (this.config.cors.origin === origin) {
      headers['Access-Control-Allow-Origin'] = origin
    }

    return headers
  }

  /**
   * Validate request
   */
  validateRequest(req: any): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    // Check rate limit
    const rateLimit = this.rateLimit(req)
    if (!rateLimit.allowed) {
      errors.push('Rate limit exceeded')
    }

    // Check CSRF for state-changing methods
    if (this.config.csrf.enabled && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(req.method)) {
      const sessionId = this.getSessionId(req)
      const csrfToken = this.getCSRFToken(req)
      
      if (!sessionId || !csrfToken || !this.verifyCSRFToken(sessionId, csrfToken)) {
        errors.push('Invalid CSRF token')
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Get client IP address
   */
  getClientIP(req: any): string {
    return req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
           req.headers['x-real-ip'] ||
           req.connection?.remoteAddress ||
           req.socket?.remoteAddress ||
           '127.0.0.1'
  }

  /**
   * Get session ID from request
   */
  private getSessionId(req: any): string | null {
    // Try to get from cookie or header
    return req.headers['x-session-id'] || 
           req.cookies?.sessionId ||
           null
  }

  /**
   * Get CSRF token from request
   */
  private getCSRFToken(req: any): string | null {
    return req.headers[this.config.csrf.headerName.toLowerCase()] ||
           req.body?._csrf ||
           null
  }

  /**
   * Sanitize input to prevent XSS
   */
  sanitizeInput(input: string): string {
    if (typeof input !== 'string') return input

    return input
      .replace(/[<>]/g, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+=/gi, '')
      .trim()
  }

  /**
   * Hash sensitive data
   */
  hashSensitiveData(data: string, salt?: string): { hash: string; salt: string } {
    const dataSalt = salt || randomBytes(16).toString('hex')
    const hash = createHash('sha256')
      .update(data + dataSalt)
      .digest('hex')
    
    return { hash, salt: dataSalt }
  }

  /**
   * Generate secure session ID
   */
  generateSessionId(): string {
    return randomBytes(32).toString('hex')
  }

  /**
   * Check if request is from trusted source
   */
  isTrustedSource(req: any): boolean {
    const trustedIPs = ['127.0.0.1', '::1']
    const clientIP = this.getClientIP(req)
    return trustedIPs.includes(clientIP)
  }
}
