/**
 * Button Component - Real implementation with KilatCSS and KilatAnim
 * Advanced button with theme support and animations
 */

import React, { forwardRef } from 'react'
import { cn } from '@/core/kilatlib/utils'
import { useHoverAnimation } from '../../core/kilatanim/hooks'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'accent' | 'outline' | 'ghost' | 'glow'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  loading?: boolean
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
  fullWidth?: boolean
  rounded?: boolean
  animated?: boolean
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  rounded = false,
  animated = true,
  className = '',
  disabled,
  ...props
}, ref) => {
  const { ref: animRef } = useHoverAnimation(
    animated ? 'scaleIn' : '',
    animated ? 'scaleOut' : '',
    { duration: 200 }
  )

  // Combine refs
  const combinedRef = (node: HTMLButtonElement) => {
    if (ref) {
      if (typeof ref === 'function') ref(node)
      else ref.current = node
    }
    if (animRef) animRef.current = node
  }

  const classes = cn(
    // Base styles
    'kilat-button',
    'relative',
    'inline-flex',
    'items-center',
    'justify-center',
    'font-medium',
    'transition-all',
    'duration-200',
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-offset-2',
    'focus:ring-primary',
    'disabled:opacity-50',
    'disabled:cursor-not-allowed',
    'disabled:pointer-events-none',

    // Variant styles
    {
      'bg-primary text-white border-primary hover:bg-primary/90 shadow-sm hover:shadow-md': variant === 'primary',
      'bg-secondary text-white border-secondary hover:bg-secondary/90 shadow-sm hover:shadow-md': variant === 'secondary',
      'bg-accent text-white border-accent hover:bg-accent/90 shadow-sm hover:shadow-md': variant === 'accent',
      'bg-transparent text-primary border-2 border-primary hover:bg-primary hover:text-white': variant === 'outline',
      'bg-transparent text-text border-transparent hover:bg-surface hover:text-primary': variant === 'ghost',
      'bg-primary text-white border-primary hover:bg-primary/90 shadow-glow hover:shadow-glow-lg animate-kilat-glow': variant === 'glow'
    },

    // Size styles
    {
      'px-2 py-1 text-xs min-h-[24px]': size === 'xs',
      'px-3 py-1.5 text-sm min-h-[32px]': size === 'sm',
      'px-4 py-2 text-base min-h-[40px]': size === 'md',
      'px-6 py-3 text-lg min-h-[48px]': size === 'lg',
      'px-8 py-4 text-xl min-h-[56px]': size === 'xl'
    },

    // Additional styles
    {
      'w-full': fullWidth,
      'rounded-full': rounded,
      'rounded-md': !rounded,
      'cursor-wait': loading
    },

    className
  )

  const iconClasses = cn({
    'w-3 h-3': size === 'xs',
    'w-4 h-4': size === 'sm',
    'w-5 h-5': size === 'md',
    'w-6 h-6': size === 'lg',
    'w-7 h-7': size === 'xl'
  })

  const LoadingSpinner = () => (
    <svg
      className={cn('animate-spin', iconClasses)}
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  )

  return (
    <button
      ref={combinedRef}
      className={classes}
      disabled={disabled || loading}
      {...props}
    >
      {loading && <LoadingSpinner />}

      {!loading && icon && iconPosition === 'left' && (
        <span className={cn(iconClasses, children ? 'mr-2' : '')}>
          {icon}
        </span>
      )}

      {!loading && children && (
        <span className={loading ? 'opacity-0' : ''}>{children}</span>
      )}

      {!loading && icon && iconPosition === 'right' && (
        <span className={cn(iconClasses, children ? 'ml-2' : '')}>
          {icon}
        </span>
      )}
    </button>
  )
})

Button.displayName = 'Button'
