/**
 * Button - Reusable component
 */

import { ReactNode } from 'react'
import { cn } from '@/core/kilatlib/utils'

interface ButtonProps {
  children?: ReactNode
  className?: string
  variant?: 'default' | 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
  onClick?: () => void
}

export function Button({
  children,
  className,
  variant = 'default',
  size = 'md',
  onClick,
  ...props
}: ButtonProps) {
  return (
    <button
      className={cn(
        'kilat-button',
        // Base styles
        'inline-flex items-center justify-center rounded-lg transition-all duration-200 cursor-pointer',
        // Variant styles
        {
          'bg-slate-800 border border-slate-600 text-white hover:bg-slate-700': variant === 'default',
          'bg-blue-600 text-white hover:bg-blue-700': variant === 'primary',
          'bg-purple-600 text-white hover:bg-purple-700': variant === 'secondary',
        },
        // Size styles
        {
          'px-3 py-1.5 text-sm': size === 'sm',
          'px-4 py-2': size === 'md',
          'px-6 py-3 text-lg': size === 'lg',
        },
        className
      )}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  )
}

// Export alias for easier imports
export const KilatButton = Button
