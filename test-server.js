/**
 * Simple Test Server for Kilat.js
 */

import { createServer } from 'http'

const server = createServer((req, res) => {
  console.log(`${req.method} ${req.url}`)
  
  res.setHeader('Content-Type', 'text/html')
  res.writeHead(200)
  res.end(`
<!DOCTYPE html>
<html>
<head>
    <title>Kilat.js Test Server</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-slate-900 text-white min-h-screen flex items-center justify-center">
    <div class="text-center">
        <h1 class="text-6xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent">
            ⚡ Kilat.js
        </h1>
        <p class="text-xl text-slate-400 mb-8">Standalone Framework - Running Successfully!</p>
        <div class="grid grid-cols-2 gap-4 max-w-md mx-auto">
            <div class="bg-slate-800 p-4 rounded-lg">
                <div class="text-2xl font-bold text-green-400">✅</div>
                <div class="text-sm">No Next.js</div>
            </div>
            <div class="bg-slate-800 p-4 rounded-lg">
                <div class="text-2xl font-bold text-green-400">✅</div>
                <div class="text-sm">No Vite</div>
            </div>
            <div class="bg-slate-800 p-4 rounded-lg">
                <div class="text-2xl font-bold text-green-400">✅</div>
                <div class="text-sm">No Express</div>
            </div>
            <div class="bg-slate-800 p-4 rounded-lg">
                <div class="text-2xl font-bold text-green-400">✅</div>
                <div class="text-sm">Pure Node.js</div>
            </div>
        </div>
        <p class="text-sm text-slate-500 mt-8">Server running on port 3000</p>
    </div>
</body>
</html>
  `)
})

const PORT = 3000
server.listen(PORT, () => {
  console.log(`⚡ Kilat.js Test Server running on http://localhost:${PORT}`)
  console.log('🚀 100% Independent - No framework dependencies!')
  console.log('✅ Ready to serve requests!')
})
