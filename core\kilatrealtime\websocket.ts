/**
 * KilatRealtime - WebSocket & Real-time Data Pipeline
 * Enterprise-grade real-time communication system
 */

import { createHash, randomBytes } from 'crypto'

export interface WebSocketConfig {
  port?: number
  path?: string
  heartbeatInterval?: number
  maxConnections?: number
  compression?: boolean
  authentication?: boolean
}

export interface ClientConnection {
  id: string
  socket: any
  userId?: string
  channels: Set<string>
  lastPing: number
  metadata: {
    ip: string
    userAgent: string
    connectedAt: Date
    lastActivity: Date
  }
}

export interface RealtimeMessage {
  id: string
  type: 'data' | 'event' | 'notification' | 'system'
  channel: string
  payload: any
  timestamp: Date
  sender?: string
  recipients?: string[]
}

export interface Channel {
  name: string
  type: 'public' | 'private' | 'presence'
  subscribers: Set<string>
  metadata: {
    createdAt: Date
    lastActivity: Date
    messageCount: number
  }
}

export class KilatWebSocketServer {
  private config: WebSocketConfig
  private connections: Map<string, ClientConnection> = new Map()
  private channels: Map<string, Channel> = new Map()
  private messageHistory: Map<string, RealtimeMessage[]> = new Map()
  private server: any
  private heartbeatTimer: NodeJS.Timeout | null = null

  constructor(config: WebSocketConfig = {}) {
    this.config = {
      port: 3001,
      path: '/ws',
      heartbeatInterval: 30000, // 30 seconds
      maxConnections: 1000,
      compression: true,
      authentication: false,
      ...config
    }

    this.initializeDefaultChannels()
  }

  /**
   * Initialize default channels
   */
  private initializeDefaultChannels(): void {
    const defaultChannels = [
      { name: 'system', type: 'public' as const },
      { name: 'stats', type: 'public' as const },
      { name: 'notifications', type: 'public' as const },
      { name: 'dashboard', type: 'public' as const },
      { name: 'orders', type: 'private' as const },
      { name: 'admin', type: 'private' as const }
    ]

    defaultChannels.forEach(channel => {
      this.channels.set(channel.name, {
        name: channel.name,
        type: channel.type,
        subscribers: new Set(),
        metadata: {
          createdAt: new Date(),
          lastActivity: new Date(),
          messageCount: 0
        }
      })
    })
  }

  /**
   * Start WebSocket server
   */
  async start(): Promise<void> {
    try {
      // Try to use ws library if available
      const WebSocket = await import('ws').then(m => m.WebSocketServer)
      
      this.server = new WebSocket({
        port: this.config.port,
        path: this.config.path
      })

      this.server.on('connection', this.handleConnection.bind(this))
      this.startHeartbeat()

      console.log(`🔄 KilatWebSocket server started on port ${this.config.port}`)
      
    } catch (error) {
      console.warn('⚠️ WebSocket library not available, using fallback')
      this.startFallbackServer()
    }
  }

  /**
   * Start fallback server (Server-Sent Events)
   */
  private startFallbackServer(): void {
    // Fallback to Server-Sent Events
    console.log('🔄 Using Server-Sent Events fallback for real-time features')
  }

  /**
   * Handle new WebSocket connection
   */
  private handleConnection(socket: any, request: any): void {
    const connectionId = this.generateConnectionId()
    const clientIP = request.socket.remoteAddress || 'unknown'
    const userAgent = request.headers['user-agent'] || 'unknown'

    // Check connection limit
    if (this.connections.size >= this.config.maxConnections!) {
      socket.close(1013, 'Server overloaded')
      return
    }

    const connection: ClientConnection = {
      id: connectionId,
      socket,
      channels: new Set(),
      lastPing: Date.now(),
      metadata: {
        ip: clientIP,
        userAgent,
        connectedAt: new Date(),
        lastActivity: new Date()
      }
    }

    this.connections.set(connectionId, connection)

    // Set up event handlers
    socket.on('message', (data: Buffer) => {
      this.handleMessage(connectionId, data)
    })

    socket.on('close', () => {
      this.handleDisconnection(connectionId)
    })

    socket.on('error', (error: Error) => {
      console.error(`WebSocket error for ${connectionId}:`, error)
      this.handleDisconnection(connectionId)
    })

    // Send welcome message
    this.sendToConnection(connectionId, {
      type: 'system',
      channel: 'system',
      payload: {
        message: 'Connected to Kilat.js real-time server',
        connectionId,
        timestamp: new Date()
      }
    })

    console.log(`🔗 New WebSocket connection: ${connectionId} (${this.connections.size} total)`)
  }

  /**
   * Handle incoming message
   */
  private handleMessage(connectionId: string, data: Buffer): void {
    try {
      const connection = this.connections.get(connectionId)
      if (!connection) return

      connection.metadata.lastActivity = new Date()

      const message = JSON.parse(data.toString())
      
      switch (message.type) {
        case 'subscribe':
          this.handleSubscribe(connectionId, message.channel)
          break
        case 'unsubscribe':
          this.handleUnsubscribe(connectionId, message.channel)
          break
        case 'message':
          this.handleChannelMessage(connectionId, message)
          break
        case 'ping':
          this.handlePing(connectionId)
          break
        default:
          console.warn(`Unknown message type: ${message.type}`)
      }

    } catch (error) {
      console.error(`Error handling message from ${connectionId}:`, error)
    }
  }

  /**
   * Handle channel subscription
   */
  private handleSubscribe(connectionId: string, channelName: string): void {
    const connection = this.connections.get(connectionId)
    const channel = this.channels.get(channelName)

    if (!connection || !channel) return

    // Check permissions for private channels
    if (channel.type === 'private' && !this.canAccessPrivateChannel(connection, channelName)) {
      this.sendToConnection(connectionId, {
        type: 'error',
        channel: 'system',
        payload: { message: 'Access denied to private channel' }
      })
      return
    }

    connection.channels.add(channelName)
    channel.subscribers.add(connectionId)
    channel.metadata.lastActivity = new Date()

    this.sendToConnection(connectionId, {
      type: 'system',
      channel: 'system',
      payload: {
        message: `Subscribed to channel: ${channelName}`,
        channel: channelName,
        subscriberCount: channel.subscribers.size
      }
    })

    // Send recent messages if available
    const recentMessages = this.messageHistory.get(channelName)?.slice(-10) || []
    recentMessages.forEach(msg => {
      this.sendToConnection(connectionId, msg)
    })

    console.log(`📺 ${connectionId} subscribed to ${channelName}`)
  }

  /**
   * Handle channel unsubscription
   */
  private handleUnsubscribe(connectionId: string, channelName: string): void {
    const connection = this.connections.get(connectionId)
    const channel = this.channels.get(channelName)

    if (!connection || !channel) return

    connection.channels.delete(channelName)
    channel.subscribers.delete(connectionId)

    this.sendToConnection(connectionId, {
      type: 'system',
      channel: 'system',
      payload: { message: `Unsubscribed from channel: ${channelName}` }
    })

    console.log(`📺 ${connectionId} unsubscribed from ${channelName}`)
  }

  /**
   * Handle channel message
   */
  private handleChannelMessage(connectionId: string, message: any): void {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    const realtimeMessage: RealtimeMessage = {
      id: this.generateMessageId(),
      type: 'data',
      channel: message.channel,
      payload: message.payload,
      timestamp: new Date(),
      sender: connectionId
    }

    this.broadcastToChannel(message.channel, realtimeMessage, connectionId)
    this.storeMessage(realtimeMessage)
  }

  /**
   * Handle ping
   */
  private handlePing(connectionId: string): void {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    connection.lastPing = Date.now()
    
    this.sendToConnection(connectionId, {
      type: 'pong',
      channel: 'system',
      payload: { timestamp: new Date() }
    })
  }

  /**
   * Handle disconnection
   */
  private handleDisconnection(connectionId: string): void {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    // Remove from all channels
    connection.channels.forEach(channelName => {
      const channel = this.channels.get(channelName)
      if (channel) {
        channel.subscribers.delete(connectionId)
      }
    })

    this.connections.delete(connectionId)
    console.log(`🔌 WebSocket disconnected: ${connectionId} (${this.connections.size} remaining)`)
  }

  /**
   * Broadcast message to channel
   */
  broadcastToChannel(channelName: string, message: Partial<RealtimeMessage>, excludeConnection?: string): void {
    const channel = this.channels.get(channelName)
    if (!channel) return

    const fullMessage: RealtimeMessage = {
      id: message.id || this.generateMessageId(),
      type: message.type || 'data',
      channel: channelName,
      payload: message.payload,
      timestamp: message.timestamp || new Date(),
      sender: message.sender
    }

    channel.subscribers.forEach(connectionId => {
      if (connectionId !== excludeConnection) {
        this.sendToConnection(connectionId, fullMessage)
      }
    })

    this.storeMessage(fullMessage)
    channel.metadata.lastActivity = new Date()
    channel.metadata.messageCount++
  }

  /**
   * Send message to specific connection
   */
  private sendToConnection(connectionId: string, message: Partial<RealtimeMessage>): void {
    const connection = this.connections.get(connectionId)
    if (!connection || connection.socket.readyState !== 1) return

    try {
      connection.socket.send(JSON.stringify(message))
    } catch (error) {
      console.error(`Error sending message to ${connectionId}:`, error)
      this.handleDisconnection(connectionId)
    }
  }

  /**
   * Store message in history
   */
  private storeMessage(message: RealtimeMessage): void {
    if (!this.messageHistory.has(message.channel)) {
      this.messageHistory.set(message.channel, [])
    }

    const history = this.messageHistory.get(message.channel)!
    history.push(message)

    // Keep only last 100 messages per channel
    if (history.length > 100) {
      history.splice(0, history.length - 100)
    }
  }

  /**
   * Check if connection can access private channel
   */
  private canAccessPrivateChannel(connection: ClientConnection, channelName: string): boolean {
    // Implement your authorization logic here
    // For demo purposes, allow access if user is authenticated
    return !!connection.userId
  }

  /**
   * Start heartbeat timer
   */
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      const now = Date.now()
      const timeout = this.config.heartbeatInterval! * 2

      this.connections.forEach((connection, connectionId) => {
        if (now - connection.lastPing > timeout) {
          console.log(`💔 Heartbeat timeout for ${connectionId}`)
          connection.socket.close()
          this.handleDisconnection(connectionId)
        }
      })
    }, this.config.heartbeatInterval)
  }

  /**
   * Generate connection ID
   */
  private generateConnectionId(): string {
    return `conn_${Date.now()}_${randomBytes(8).toString('hex')}`
  }

  /**
   * Generate message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${randomBytes(8).toString('hex')}`
  }

  /**
   * Get server statistics
   */
  getStats(): any {
    return {
      connections: this.connections.size,
      channels: this.channels.size,
      totalMessages: Array.from(this.messageHistory.values())
        .reduce((sum, history) => sum + history.length, 0),
      channelStats: Array.from(this.channels.entries()).map(([name, channel]) => ({
        name,
        type: channel.type,
        subscribers: channel.subscribers.size,
        messageCount: channel.metadata.messageCount,
        lastActivity: channel.metadata.lastActivity
      }))
    }
  }

  /**
   * Stop server
   */
  async stop(): Promise<void> {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
    }

    if (this.server) {
      this.server.close()
    }

    this.connections.clear()
    console.log('🔌 WebSocket server stopped')
  }
}
