{"name": "kilat.js", "version": "1.0.0", "description": "Modern fullstack framework built from scratch - standalone and lightning fast", "main": "kilat.cli.ts", "type": "module", "scripts": {"dev": "bun run cli.ts dev", "build": "bun run cli.ts build", "start": "bun run cli.ts start", "export": "bun run cli.ts export", "generate": "bun run cli.ts generate", "test": "vitest", "test:e2e": "playwright test"}, "keywords": ["framework", "fullstack", "bun", "typescript", "ssr", "ssg", "file-based-routing", "standalone"], "author": "Kilat.js Team", "license": "MIT", "dependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0"}, "devDependencies": {"vitest": "^1.0.0", "@playwright/test": "^1.40.0", "bun-types": "^1.0.0"}, "engines": {"bun": ">=1.0.0", "node": ">=18.0.0"}, "bin": {"kilat": "./kilat.cli.ts"}, "files": ["core/", "kilat.cli.ts", "kilat.config.js", "kilatcss.config.js", "tsconfig.json"]}