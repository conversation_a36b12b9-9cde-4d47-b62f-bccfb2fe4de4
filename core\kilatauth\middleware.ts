/**
 * KilatAuth - Authentication Middleware
 * Middleware for SpeedRun runtime integration
 */

import { KilatAuthService } from './auth.service'
import { AuthUser } from './jwt'

export interface AuthMiddlewareConfig {
  publicPaths: string[]
  protectedPaths: string[]
  adminPaths: string[]
  skipCSRF: string[]
}

export interface AuthenticatedRequest extends Request {
  user?: AuthUser
  sessionId?: string
  isAuthenticated: boolean
  canAccess: (resource: string, action: string) => boolean
}

export class KilatAuthMiddleware {
  private authService: KilatAuthService
  private config: AuthMiddlewareConfig

  constructor(config?: Partial<AuthMiddlewareConfig>) {
    this.authService = new KilatAuthService()
    this.config = {
      publicPaths: [
        '/',
        '/login',
        '/register',
        '/api/auth/login',
        '/api/auth/register',
        '/api/auth/refresh',
        '/public',
        '/_kilat'
      ],
      protectedPaths: [
        '/dashboard',
        '/profile',
        '/api/users',
        '/api/orders'
      ],
      adminPaths: [
        '/admin',
        '/api/admin'
      ],
      skipCSRF: [
        '/api/auth/login',
        '/api/auth/register',
        '/api/auth/refresh'
      ],
      ...config
    }
  }

  /**
   * Main authentication middleware
   */
  async authenticate(request: Request): Promise<Response | null> {
    const url = new URL(request.url)
    const pathname = url.pathname

    // Add security headers
    const securityHeaders = this.authService.getSecurity().getSecurityHeaders()
    const corsHeaders = this.authService.getSecurity().getCORSHeaders(
      request.headers.get('origin') || undefined
    )

    // Handle CORS preflight
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: { ...corsHeaders }
      })
    }

    // Check if path is public
    if (this.isPublicPath(pathname)) {
      return null // Continue to next middleware
    }

    // Security validation
    const security = this.authService.getSecurity()
    const validation = security.validateRequest(request)
    
    if (!validation.valid) {
      return new Response(
        JSON.stringify({ error: validation.errors.join(', ') }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            ...securityHeaders,
            ...corsHeaders
          }
        }
      )
    }

    // Extract and verify token
    const authHeader = request.headers.get('Authorization')
    const token = authHeader ? this.authService.jwt.extractTokenFromHeader(authHeader) : null

    if (!token) {
      return this.unauthorizedResponse(securityHeaders, corsHeaders)
    }

    // Verify token and get user
    const user = await this.authService.verifyToken(token)
    if (!user) {
      return this.unauthorizedResponse(securityHeaders, corsHeaders)
    }

    // Check admin access for admin paths
    if (this.isAdminPath(pathname) && !this.hasAdminAccess(user)) {
      return new Response(
        JSON.stringify({ error: 'Admin access required' }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
            ...securityHeaders,
            ...corsHeaders
          }
        }
      )
    }

    // Attach user to request (this would be done in actual middleware)
    // For now, we'll store it in a way that can be accessed later
    this.attachUserToRequest(request, user)

    return null // Continue to next middleware
  }

  /**
   * Authorization middleware for specific resources
   */
  async authorize(
    request: Request,
    resource: string,
    action: string
  ): Promise<Response | null> {
    const user = this.getUserFromRequest(request)
    
    if (!user) {
      return this.unauthorizedResponse()
    }

    const canAccess = this.authService.canAccess(user, resource, action)
    
    if (!canAccess) {
      return new Response(
        JSON.stringify({ error: 'Insufficient permissions' }),
        {
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    return null // Continue
  }

  /**
   * Role-based middleware
   */
  async requireRole(request: Request, requiredRole: string): Promise<Response | null> {
    const user = this.getUserFromRequest(request)
    
    if (!user) {
      return this.unauthorizedResponse()
    }

    if (user.role !== requiredRole && !this.hasAdminAccess(user)) {
      return new Response(
        JSON.stringify({ error: `Role '${requiredRole}' required` }),
        {
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    return null
  }

  /**
   * Permission-based middleware
   */
  async requirePermission(request: Request, permission: string): Promise<Response | null> {
    const user = this.getUserFromRequest(request)
    
    if (!user) {
      return this.unauthorizedResponse()
    }

    if (!user.permissions.includes(permission)) {
      return new Response(
        JSON.stringify({ error: `Permission '${permission}' required` }),
        {
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }

    return null
  }

  /**
   * Check if path is public
   */
  private isPublicPath(pathname: string): boolean {
    return this.config.publicPaths.some(path => {
      if (path.endsWith('*')) {
        return pathname.startsWith(path.slice(0, -1))
      }
      return pathname === path || pathname.startsWith(path + '/')
    })
  }

  /**
   * Check if path requires admin access
   */
  private isAdminPath(pathname: string): boolean {
    return this.config.adminPaths.some(path => {
      if (path.endsWith('*')) {
        return pathname.startsWith(path.slice(0, -1))
      }
      return pathname === path || pathname.startsWith(path + '/')
    })
  }

  /**
   * Check if user has admin access
   */
  private hasAdminAccess(user: AuthUser): boolean {
    return user.role === 'admin' || 
           user.role === 'super_admin' ||
           user.permissions.includes('system:admin')
  }

  /**
   * Create unauthorized response
   */
  private unauthorizedResponse(
    securityHeaders?: Record<string, string>,
    corsHeaders?: Record<string, string>
  ): Response {
    return new Response(
      JSON.stringify({ error: 'Authentication required' }),
      {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          'WWW-Authenticate': 'Bearer',
          ...securityHeaders,
          ...corsHeaders
        }
      }
    )
  }

  /**
   * Attach user to request (simplified for demo)
   */
  private attachUserToRequest(request: Request, user: AuthUser): void {
    // In a real implementation, this would modify the request object
    // For now, we'll use a WeakMap or similar approach
    (request as any).user = user
    ;(request as any).isAuthenticated = true
    ;(request as any).canAccess = (resource: string, action: string) => {
      return this.authService.canAccess(user, resource, action)
    }
  }

  /**
   * Get user from request
   */
  private getUserFromRequest(request: Request): AuthUser | null {
    return (request as any).user || null
  }

  /**
   * Get auth service instance
   */
  getAuthService(): KilatAuthService {
    return this.authService
  }

  /**
   * Update middleware config
   */
  updateConfig(config: Partial<AuthMiddlewareConfig>): void {
    this.config = { ...this.config, ...config }
  }

  /**
   * Add public path
   */
  addPublicPath(path: string): void {
    if (!this.config.publicPaths.includes(path)) {
      this.config.publicPaths.push(path)
    }
  }

  /**
   * Add protected path
   */
  addProtectedPath(path: string): void {
    if (!this.config.protectedPaths.includes(path)) {
      this.config.protectedPaths.push(path)
    }
  }

  /**
   * Add admin path
   */
  addAdminPath(path: string): void {
    if (!this.config.adminPaths.includes(path)) {
      this.config.adminPaths.push(path)
    }
  }
}
