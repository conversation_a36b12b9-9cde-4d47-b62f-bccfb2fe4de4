/**
 * <PERSON><PERSON><PERSON>oader - Advanced Loading Components
 * Beautiful loading states with animations
 */

export interface LoaderProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'spinner' | 'dots' | 'pulse' | 'wave' | 'skeleton'
  color?: 'primary' | 'secondary' | 'accent' | 'white'
  text?: string
  className?: string
}

export interface SkeletonProps {
  width?: string | number
  height?: string | number
  className?: string
  animate?: boolean
}

/**
 * Main KilatLoader component
 */
export function KilatLoader({ 
  size = 'md', 
  variant = 'spinner', 
  color = 'primary',
  text,
  className = ''
}: LoaderProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  }

  const colorClasses = {
    primary: 'text-blue-500 border-blue-500',
    secondary: 'text-purple-500 border-purple-500',
    accent: 'text-green-500 border-green-500',
    white: 'text-white border-white'
  }

  const baseClasses = `${sizeClasses[size]} ${colorClasses[color]} ${className}`

  const renderLoader = () => {
    switch (variant) {
      case 'spinner':
        return (
          <div className={`${baseClasses} border-2 border-t-transparent rounded-full animate-spin`} />
        )
      
      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={`${sizeClasses[size]} ${colorClasses[color]} bg-current rounded-full animate-pulse`}
                style={{ animationDelay: `${i * 0.2}s` }}
              />
            ))}
          </div>
        )
      
      case 'pulse':
        return (
          <div className={`${baseClasses} bg-current rounded-full animate-pulse`} />
        )
      
      case 'wave':
        return (
          <div className="flex items-end space-x-1">
            {[0, 1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className={`w-1 bg-current animate-bounce`}
                style={{ 
                  height: `${8 + (i % 2) * 4}px`,
                  animationDelay: `${i * 0.1}s` 
                }}
              />
            ))}
          </div>
        )
      
      case 'skeleton':
        return <KilatSkeleton className={baseClasses} />
      
      default:
        return (
          <div className={`${baseClasses} border-2 border-t-transparent rounded-full animate-spin`} />
        )
    }
  }

  return (
    <div className="flex flex-col items-center justify-center space-y-2">
      {renderLoader()}
      {text && (
        <p className={`text-sm ${colorClasses[color]} animate-pulse`}>
          {text}
        </p>
      )}
    </div>
  )
}

/**
 * Skeleton loader component
 */
export function KilatSkeleton({ 
  width = '100%', 
  height = '1rem', 
  className = '',
  animate = true 
}: SkeletonProps) {
  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
  }

  return (
    <div
      className={`bg-slate-300 dark:bg-slate-700 rounded ${animate ? 'animate-pulse' : ''} ${className}`}
      style={style}
    />
  )
}

/**
 * Loading overlay component
 */
export function KilatLoadingOverlay({ 
  isLoading, 
  children, 
  text = 'Loading...',
  variant = 'spinner',
  className = ''
}: {
  isLoading: boolean
  children: React.ReactNode
  text?: string
  variant?: LoaderProps['variant']
  className?: string
}) {
  return (
    <div className={`relative ${className}`}>
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
          <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-xl">
            <KilatLoader variant={variant} text={text} color="primary" />
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * Inline loading component
 */
export function KilatInlineLoader({ 
  size = 'sm', 
  className = '' 
}: { 
  size?: LoaderProps['size']
  className?: string 
}) {
  return (
    <KilatLoader 
      size={size} 
      variant="spinner" 
      color="primary" 
      className={`inline-block ${className}`} 
    />
  )
}

/**
 * Button with loading state
 */
export function KilatLoadingButton({
  isLoading,
  children,
  onClick,
  disabled,
  className = '',
  loadingText = 'Loading...',
  ...props
}: {
  isLoading: boolean
  children: React.ReactNode
  onClick?: () => void
  disabled?: boolean
  className?: string
  loadingText?: string
  [key: string]: any
}) {
  return (
    <button
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`relative flex items-center justify-center space-x-2 ${className} ${
        isLoading || disabled ? 'opacity-50 cursor-not-allowed' : ''
      }`}
      {...props}
    >
      {isLoading && <KilatInlineLoader size="sm" />}
      <span>{isLoading ? loadingText : children}</span>
    </button>
  )
}

/**
 * Card skeleton for loading states
 */
export function KilatCardSkeleton({ className = '' }: { className?: string }) {
  return (
    <div className={`p-6 border border-slate-200 dark:border-slate-700 rounded-lg ${className}`}>
      <div className="animate-pulse">
        <div className="flex items-center space-x-4 mb-4">
          <KilatSkeleton width={40} height={40} className="rounded-full" />
          <div className="flex-1">
            <KilatSkeleton height={16} className="mb-2" />
            <KilatSkeleton height={12} width="60%" />
          </div>
        </div>
        <KilatSkeleton height={12} className="mb-2" />
        <KilatSkeleton height={12} className="mb-2" />
        <KilatSkeleton height={12} width="80%" />
      </div>
    </div>
  )
}

/**
 * Table skeleton for loading states
 */
export function KilatTableSkeleton({ 
  rows = 5, 
  columns = 4,
  className = '' 
}: { 
  rows?: number
  columns?: number
  className?: string 
}) {
  return (
    <div className={`space-y-3 ${className}`}>
      {/* Header */}
      <div className="flex space-x-4">
        {Array.from({ length: columns }).map((_, i) => (
          <KilatSkeleton key={i} height={16} className="flex-1" />
        ))}
      </div>
      
      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <KilatSkeleton key={colIndex} height={12} className="flex-1" />
          ))}
        </div>
      ))}
    </div>
  )
}

/**
 * Progress bar component
 */
export function KilatProgressBar({
  progress,
  className = '',
  showPercentage = true,
  color = 'primary'
}: {
  progress: number
  className?: string
  showPercentage?: boolean
  color?: LoaderProps['color']
}) {
  const colorClasses = {
    primary: 'bg-blue-500',
    secondary: 'bg-purple-500',
    accent: 'bg-green-500',
    white: 'bg-white'
  }

  const clampedProgress = Math.max(0, Math.min(100, progress))

  return (
    <div className={`w-full ${className}`}>
      <div className="flex justify-between items-center mb-1">
        {showPercentage && (
          <span className="text-sm text-slate-600 dark:text-slate-400">
            {Math.round(clampedProgress)}%
          </span>
        )}
      </div>
      <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ease-out ${colorClasses[color]}`}
          style={{ width: `${clampedProgress}%` }}
        />
      </div>
    </div>
  )
}
