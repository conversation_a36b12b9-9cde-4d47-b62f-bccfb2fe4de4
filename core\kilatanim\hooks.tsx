/**
 * KilatAnim React Hooks
 * React hooks for easy animation integration
 */

import { useEffect, useRef, useState } from 'react'
import { kilatAnim, AnimationConfig } from './index'

/**
 * Hook for animating element on mount
 */
export function useKilatAnim(
  preset: string,
  config?: Partial<AnimationConfig>,
  trigger: boolean = true
) {
  const ref = useRef<HTMLElement>(null)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (ref.current && trigger && !isAnimating) {
      setIsAnimating(true)
      const animation = kilatAnim.animate(ref.current, preset, config)
      
      if (animation) {
        animation.addEventListener('finish', () => {
          setIsAnimating(false)
        })
      }
    }
  }, [preset, config, trigger, isAnimating])

  return { ref, isAnimating }
}

/**
 * Hook for animating element on scroll into view
 */
export function useScrollAnimation(
  preset: string,
  options?: {
    threshold?: number
    rootMargin?: string
    once?: boolean
  }
) {
  const ref = useRef<HTMLElement>(null)
  const [isVisible, setIsVisible] = useState(false)
  const [hasAnimated, setHasAnimated] = useState(false)

  useEffect(() => {
    const element = ref.current
    if (!element) return

    const { threshold = 0.1, rootMargin = '0px', once = true } = options || {}

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && (!once || !hasAnimated)) {
            setIsVisible(true)
            kilatAnim.animate(entry.target, preset)
            setHasAnimated(true)
            
            if (once) {
              observer.unobserve(entry.target)
            }
          } else if (!entry.isIntersecting && !once) {
            setIsVisible(false)
          }
        })
      },
      { threshold, rootMargin }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [preset, options, hasAnimated])

  return { ref, isVisible, hasAnimated }
}

/**
 * Hook for hover animations
 */
export function useHoverAnimation(
  enterPreset: string,
  leavePreset?: string,
  config?: Partial<AnimationConfig>
) {
  const ref = useRef<HTMLElement>(null)
  const [isHovered, setIsHovered] = useState(false)

  useEffect(() => {
    const element = ref.current
    if (!element) return

    const handleMouseEnter = () => {
      setIsHovered(true)
      kilatAnim.animate(element, enterPreset, config)
    }

    const handleMouseLeave = () => {
      setIsHovered(false)
      if (leavePreset) {
        kilatAnim.animate(element, leavePreset, config)
      }
    }

    element.addEventListener('mouseenter', handleMouseEnter)
    element.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter)
      element.removeEventListener('mouseleave', handleMouseLeave)
    }
  }, [enterPreset, leavePreset, config])

  return { ref, isHovered }
}

/**
 * Hook for staggered animations
 */
export function useStaggeredAnimation(
  preset: string,
  staggerDelay: number = 100,
  config?: Partial<AnimationConfig>
) {
  const containerRef = useRef<HTMLElement>(null)
  const [isAnimating, setIsAnimating] = useState(false)

  const animate = () => {
    if (!containerRef.current || isAnimating) return

    setIsAnimating(true)
    const children = Array.from(containerRef.current.children) as HTMLElement[]
    
    children.forEach((child, index) => {
      setTimeout(() => {
        kilatAnim.animate(child, preset, {
          ...config,
          delay: (config?.delay || 0) + (index * staggerDelay)
        })
        
        if (index === children.length - 1) {
          setTimeout(() => setIsAnimating(false), (config?.duration || 600))
        }
      }, index * staggerDelay)
    })
  }

  return { ref: containerRef, animate, isAnimating }
}

/**
 * Hook for sequence animations
 */
export function useSequenceAnimation(
  sequence: Array<{
    preset: string
    config?: Partial<AnimationConfig>
    delay?: number
  }>
) {
  const ref = useRef<HTMLElement>(null)
  const [currentStep, setCurrentStep] = useState(-1)
  const [isPlaying, setIsPlaying] = useState(false)

  const play = () => {
    if (!ref.current || isPlaying) return

    setIsPlaying(true)
    setCurrentStep(0)

    let totalDelay = 0

    sequence.forEach((step, index) => {
      totalDelay += step.delay || 0
      
      setTimeout(() => {
        if (ref.current) {
          setCurrentStep(index)
          const animation = kilatAnim.animate(ref.current, step.preset, step.config)
          
          if (animation && index === sequence.length - 1) {
            animation.addEventListener('finish', () => {
              setIsPlaying(false)
              setCurrentStep(-1)
            })
          }
        }
      }, totalDelay)

      totalDelay += step.config?.duration || 600
    })
  }

  return { ref, play, currentStep, isPlaying }
}

/**
 * Hook for continuous animations
 */
export function useContinuousAnimation(
  preset: string,
  config?: Partial<AnimationConfig>
) {
  const ref = useRef<HTMLElement>(null)
  const [isRunning, setIsRunning] = useState(false)
  const animationRef = useRef<Animation | null>(null)

  const start = () => {
    if (!ref.current || isRunning) return

    setIsRunning(true)
    animationRef.current = kilatAnim.animate(ref.current, preset, {
      ...config,
      iterations: 'infinite'
    })
  }

  const stop = () => {
    if (animationRef.current) {
      animationRef.current.cancel()
      animationRef.current = null
    }
    setIsRunning(false)
  }

  const toggle = () => {
    if (isRunning) {
      stop()
    } else {
      start()
    }
  }

  useEffect(() => {
    return () => {
      if (animationRef.current) {
        animationRef.current.cancel()
      }
    }
  }, [])

  return { ref, start, stop, toggle, isRunning }
}

/**
 * Hook for gesture-based animations
 */
export function useGestureAnimation() {
  const ref = useRef<HTMLElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [position, setPosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const element = ref.current
    if (!element) return

    let startPos = { x: 0, y: 0 }
    let currentPos = { x: 0, y: 0 }

    const handleStart = (e: MouseEvent | TouchEvent) => {
      setIsDragging(true)
      const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
      const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY
      startPos = { x: clientX, y: clientY }
      
      kilatAnim.animate(element, 'scaleIn', { duration: 200 })
    }

    const handleMove = (e: MouseEvent | TouchEvent) => {
      if (!isDragging) return
      
      const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
      const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY
      
      currentPos = {
        x: clientX - startPos.x,
        y: clientY - startPos.y
      }
      
      setPosition(currentPos)
      element.style.transform = `translate(${currentPos.x}px, ${currentPos.y}px)`
    }

    const handleEnd = () => {
      setIsDragging(false)
      kilatAnim.animate(element, 'scaleOut', { duration: 200 })
      
      // Snap back animation
      kilatAnim.createCustomAnimation(element, [
        { transform: `translate(${currentPos.x}px, ${currentPos.y}px)` },
        { transform: 'translate(0px, 0px)' }
      ], { duration: 300, easing: 'cubic-bezier(0.34, 1.56, 0.64, 1)' })
      
      setPosition({ x: 0, y: 0 })
    }

    element.addEventListener('mousedown', handleStart)
    element.addEventListener('touchstart', handleStart)
    document.addEventListener('mousemove', handleMove)
    document.addEventListener('touchmove', handleMove)
    document.addEventListener('mouseup', handleEnd)
    document.addEventListener('touchend', handleEnd)

    return () => {
      element.removeEventListener('mousedown', handleStart)
      element.removeEventListener('touchstart', handleStart)
      document.removeEventListener('mousemove', handleMove)
      document.removeEventListener('touchmove', handleMove)
      document.removeEventListener('mouseup', handleEnd)
      document.removeEventListener('touchend', handleEnd)
    }
  }, [isDragging])

  return { ref, isDragging, position }
}
