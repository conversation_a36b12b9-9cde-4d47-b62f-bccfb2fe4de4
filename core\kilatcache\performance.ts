/**
 * KilatCache - Performance Monitor
 * Advanced performance monitoring and optimization
 */

export interface PerformanceMetric {
  name: string
  value: number
  unit: string
  timestamp: number
  tags?: Record<string, string>
}

export interface RequestMetrics {
  id: string
  method: string
  path: string
  startTime: number
  endTime?: number
  duration?: number
  statusCode?: number
  responseSize?: number
  memoryUsage?: NodeJS.MemoryUsage
  cacheHit?: boolean
  dbQueries?: number
  errors?: string[]
}

export interface PerformanceReport {
  timeRange: { start: number; end: number }
  totalRequests: number
  averageResponseTime: number
  p95ResponseTime: number
  p99ResponseTime: number
  errorRate: number
  cacheHitRate: number
  memoryUsage: {
    average: number
    peak: number
    current: number
  }
  slowestEndpoints: Array<{
    path: string
    averageTime: number
    requestCount: number
  }>
  topErrors: Array<{
    error: string
    count: number
    percentage: number
  }>
}

export class KilatPerformanceMonitor {
  private metrics: Map<string, PerformanceMetric[]> = new Map()
  private activeRequests: Map<string, RequestMetrics> = new Map()
  private completedRequests: RequestMetrics[] = []
  private maxHistorySize: number = 10000
  private alertThresholds: Map<string, number> = new Map()

  constructor() {
    this.setupDefaultThresholds()
    this.startMemoryMonitoring()
  }

  /**
   * Start request tracking
   */
  startRequest(method: string, path: string): string {
    const id = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const request: RequestMetrics = {
      id,
      method,
      path,
      startTime: Date.now(),
      memoryUsage: process.memoryUsage()
    }

    this.activeRequests.set(id, request)
    return id
  }

  /**
   * End request tracking
   */
  endRequest(
    id: string, 
    statusCode: number, 
    responseSize?: number,
    cacheHit?: boolean,
    dbQueries?: number,
    errors?: string[]
  ): void {
    const request = this.activeRequests.get(id)
    if (!request) return

    const endTime = Date.now()
    request.endTime = endTime
    request.duration = endTime - request.startTime
    request.statusCode = statusCode
    request.responseSize = responseSize
    request.cacheHit = cacheHit
    request.dbQueries = dbQueries
    request.errors = errors

    this.activeRequests.delete(id)
    this.completedRequests.push(request)

    // Keep only recent requests
    if (this.completedRequests.length > this.maxHistorySize) {
      this.completedRequests = this.completedRequests.slice(-this.maxHistorySize)
    }

    // Record metrics
    this.recordMetric('response_time', request.duration!, 'ms', {
      method: request.method,
      path: request.path,
      status: statusCode.toString()
    })

    if (responseSize) {
      this.recordMetric('response_size', responseSize, 'bytes', {
        method: request.method,
        path: request.path
      })
    }

    if (dbQueries !== undefined) {
      this.recordMetric('db_queries', dbQueries, 'count', {
        path: request.path
      })
    }

    // Check for performance alerts
    this.checkAlerts(request)
  }

  /**
   * Record custom metric
   */
  recordMetric(
    name: string, 
    value: number, 
    unit: string, 
    tags?: Record<string, string>
  ): void {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: Date.now(),
      tags
    }

    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }

    const metricHistory = this.metrics.get(name)!
    metricHistory.push(metric)

    // Keep only recent metrics (last hour)
    const oneHourAgo = Date.now() - (60 * 60 * 1000)
    this.metrics.set(name, metricHistory.filter(m => m.timestamp > oneHourAgo))
  }

  /**
   * Get performance report
   */
  getPerformanceReport(timeRangeMs: number = 60 * 60 * 1000): PerformanceReport {
    const now = Date.now()
    const startTime = now - timeRangeMs
    
    const recentRequests = this.completedRequests.filter(
      req => req.startTime >= startTime && req.duration !== undefined
    )

    const responseTimes = recentRequests.map(req => req.duration!).sort((a, b) => a - b)
    const errors = recentRequests.filter(req => req.statusCode! >= 400)
    const cacheHits = recentRequests.filter(req => req.cacheHit === true)

    // Calculate percentiles
    const p95Index = Math.floor(responseTimes.length * 0.95)
    const p99Index = Math.floor(responseTimes.length * 0.99)

    // Group by endpoint
    const endpointStats = new Map<string, { times: number[]; count: number }>()
    recentRequests.forEach(req => {
      const key = `${req.method} ${req.path}`
      if (!endpointStats.has(key)) {
        endpointStats.set(key, { times: [], count: 0 })
      }
      const stats = endpointStats.get(key)!
      stats.times.push(req.duration!)
      stats.count++
    })

    // Get slowest endpoints
    const slowestEndpoints = Array.from(endpointStats.entries())
      .map(([path, stats]) => ({
        path,
        averageTime: stats.times.reduce((a, b) => a + b, 0) / stats.times.length,
        requestCount: stats.count
      }))
      .sort((a, b) => b.averageTime - a.averageTime)
      .slice(0, 10)

    // Get top errors
    const errorCounts = new Map<string, number>()
    errors.forEach(req => {
      req.errors?.forEach(error => {
        errorCounts.set(error, (errorCounts.get(error) || 0) + 1)
      })
    })

    const topErrors = Array.from(errorCounts.entries())
      .map(([error, count]) => ({
        error,
        count,
        percentage: (count / errors.length) * 100
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    // Memory usage
    const memoryMetrics = this.metrics.get('memory_usage') || []
    const recentMemory = memoryMetrics.filter(m => m.timestamp >= startTime)
    const memoryValues = recentMemory.map(m => m.value)

    return {
      timeRange: { start: startTime, end: now },
      totalRequests: recentRequests.length,
      averageResponseTime: responseTimes.length > 0 
        ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
        : 0,
      p95ResponseTime: responseTimes[p95Index] || 0,
      p99ResponseTime: responseTimes[p99Index] || 0,
      errorRate: recentRequests.length > 0 
        ? (errors.length / recentRequests.length) * 100 
        : 0,
      cacheHitRate: recentRequests.length > 0 
        ? (cacheHits.length / recentRequests.length) * 100 
        : 0,
      memoryUsage: {
        average: memoryValues.length > 0 
          ? memoryValues.reduce((a, b) => a + b, 0) / memoryValues.length 
          : 0,
        peak: memoryValues.length > 0 ? Math.max(...memoryValues) : 0,
        current: process.memoryUsage().heapUsed
      },
      slowestEndpoints,
      topErrors
    }
  }

  /**
   * Get real-time metrics
   */
  getRealTimeMetrics(): {
    activeRequests: number
    memoryUsage: NodeJS.MemoryUsage
    uptime: number
    cpuUsage: NodeJS.CpuUsage
  } {
    return {
      activeRequests: this.activeRequests.size,
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime(),
      cpuUsage: process.cpuUsage()
    }
  }

  /**
   * Set alert threshold
   */
  setAlertThreshold(metric: string, threshold: number): void {
    this.alertThresholds.set(metric, threshold)
  }

  /**
   * Check for performance alerts
   */
  private checkAlerts(request: RequestMetrics): void {
    // Response time alert
    const responseTimeThreshold = this.alertThresholds.get('response_time')
    if (responseTimeThreshold && request.duration! > responseTimeThreshold) {
      console.warn(`⚠️ Slow response detected: ${request.method} ${request.path} took ${request.duration}ms`)
    }

    // Memory usage alert
    const memoryThreshold = this.alertThresholds.get('memory_usage')
    const currentMemory = process.memoryUsage().heapUsed
    if (memoryThreshold && currentMemory > memoryThreshold) {
      console.warn(`⚠️ High memory usage detected: ${Math.round(currentMemory / 1024 / 1024)}MB`)
    }

    // Error alert
    if (request.errors && request.errors.length > 0) {
      console.warn(`⚠️ Request errors: ${request.method} ${request.path}`, request.errors)
    }
  }

  /**
   * Setup default alert thresholds
   */
  private setupDefaultThresholds(): void {
    this.alertThresholds.set('response_time', 5000) // 5 seconds
    this.alertThresholds.set('memory_usage', 500 * 1024 * 1024) // 500MB
    this.alertThresholds.set('error_rate', 10) // 10%
  }

  /**
   * Start memory monitoring
   */
  private startMemoryMonitoring(): void {
    setInterval(() => {
      const memUsage = process.memoryUsage()
      this.recordMetric('memory_usage', memUsage.heapUsed, 'bytes')
      this.recordMetric('memory_total', memUsage.heapTotal, 'bytes')
      this.recordMetric('memory_external', memUsage.external, 'bytes')
    }, 30000) // Every 30 seconds
  }

  /**
   * Get metric history
   */
  getMetricHistory(name: string, timeRangeMs: number = 60 * 60 * 1000): PerformanceMetric[] {
    const metrics = this.metrics.get(name) || []
    const cutoff = Date.now() - timeRangeMs
    return metrics.filter(m => m.timestamp >= cutoff)
  }

  /**
   * Clear old data
   */
  cleanup(): void {
    const oneHourAgo = Date.now() - (60 * 60 * 1000)
    
    // Clean metrics
    for (const [name, metrics] of this.metrics.entries()) {
      this.metrics.set(name, metrics.filter(m => m.timestamp > oneHourAgo))
    }

    // Clean completed requests
    this.completedRequests = this.completedRequests.filter(
      req => req.startTime > oneHourAgo
    )
  }
}
