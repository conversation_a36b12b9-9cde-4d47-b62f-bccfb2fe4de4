/**
 * Products API Routes
 * Handles CRUD operations for products
 */

import { NextRequest } from 'next/server'
import { ProductsService } from '@/core/kilatservice/products.service'

const productsService = new ProductsService()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || undefined
    const category = searchParams.get('category') || undefined
    
    const result = await productsService.getAll({
      page,
      limit,
      search,
      category
    })
    
    return Response.json({
      success: true,
      data: result.items,
      pagination: {
        page,
        limit,
        total: result.total,
        totalPages: Math.ceil(result.total / limit)
      }
    })
    
  } catch (error) {
    console.error('Products GET Error:', error)
    return Response.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.name || !body.price || !body.category) {
      return Response.json(
        { error: 'Name, price, and category are required' },
        { status: 400 }
      )
    }
    
    const result = await productsService.create(body)
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 400 }
      )
    }
    
    return Response.json({
      success: true,
      data: result.data
    }, { status: 201 })
    
  } catch (error) {
    console.error('Products POST Error:', error)
    return Response.json(
      { error: 'Failed to create product' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return Response.json(
        { error: 'ID is required' },
        { status: 400 }
      )
    }
    
    const body = await request.json()
    const result = await productsService.update(id, body)
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 400 }
      )
    }
    
    return Response.json({
      success: true,
      data: result.data
    })
    
  } catch (error) {
    console.error('Products PUT Error:', error)
    return Response.json(
      { error: 'Failed to update product' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return Response.json(
        { error: 'ID is required' },
        { status: 400 }
      )
    }
    
    const result = await productsService.delete(id)
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 400 }
      )
    }
    
    return Response.json({
      success: true,
      message: 'Product deleted successfully'
    })
    
  } catch (error) {
    console.error('Products DELETE Error:', error)
    return Response.json(
      { error: 'Failed to delete product' },
      { status: 500 }
    )
  }
}
