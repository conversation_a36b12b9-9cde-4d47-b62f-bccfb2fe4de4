/**
 * Orders API Routes
 * Handles CRUD operations for orders
 */

import { NextRequest } from 'next/server'
import { OrdersService } from '@/core/kilatservice/orders.service'

const ordersService = new OrdersService()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    
    const result = await ordersService.getAll({
      page,
      limit
    })
    
    return Response.json({
      success: true,
      data: result.items,
      pagination: {
        page,
        limit,
        total: result.total,
        totalPages: Math.ceil(result.total / limit)
      }
    })
    
  } catch (error) {
    console.error('Orders GET Error:', error)
    return Response.json(
      { error: 'Failed to fetch orders' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const result = await ordersService.create(body)
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 400 }
      )
    }
    
    return Response.json({
      success: true,
      data: result.data
    }, { status: 201 })
    
  } catch (error) {
    console.error('Orders POST Error:', error)
    return Response.json(
      { error: 'Failed to create orders' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return Response.json(
        { error: 'ID is required' },
        { status: 400 }
      )
    }
    
    const body = await request.json()
    const result = await ordersService.update(id, body)
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 400 }
      )
    }
    
    return Response.json({
      success: true,
      data: result.data
    })
    
  } catch (error) {
    console.error('Orders PUT Error:', error)
    return Response.json(
      { error: 'Failed to update orders' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return Response.json(
        { error: 'ID is required' },
        { status: 400 }
      )
    }
    
    const result = await ordersService.delete(id)
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 400 }
      )
    }
    
    return Response.json({
      success: true,
      message: 'Orders deleted successfully'
    })
    
  } catch (error) {
    console.error('Orders DELETE Error:', error)
    return Response.json(
      { error: 'Failed to delete orders' },
      { status: 500 }
    )
  }
}
