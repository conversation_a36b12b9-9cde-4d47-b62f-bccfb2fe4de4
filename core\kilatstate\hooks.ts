/**
 * KilatState - React-like Hooks System
 * Standalone state management without React dependency
 */

type StateUpdater<T> = (newValue: T | ((prev: T) => T)) => void
type EffectCleanup = () => void
type EffectCallback = () => void | EffectCleanup
type DependencyList = ReadonlyArray<any>

interface StateHook<T> {
  value: T
  setValue: StateUpdater<T>
  subscribers: Set<() => void>
}

interface EffectHook {
  callback: EffectCallback
  dependencies: DependencyList | undefined
  cleanup?: EffectCleanup
}

class KilatStateManager {
  private states: Map<string, StateHook<any>> = new Map()
  private effects: Map<string, EffectHook> = new Map()
  private currentComponent: string | null = null
  private hookIndex: number = 0
  private isRendering: boolean = false

  /**
   * Set current component context
   */
  setCurrentComponent(componentId: string): void {
    this.currentComponent = componentId
    this.hookIndex = 0
    this.isRendering = true
  }

  /**
   * Clear current component context
   */
  clearCurrentComponent(): void {
    this.currentComponent = null
    this.hookIndex = 0
    this.isRendering = false
  }

  /**
   * Get unique hook key
   */
  private getHookKey(): string {
    if (!this.currentComponent) {
      throw new Error('Hooks can only be called during component render')
    }
    return `${this.currentComponent}:${this.hookIndex++}`
  }

  /**
   * useState implementation
   */
  useState<T>(initialValue: T): [T, StateUpdater<T>] {
    const key = this.getHookKey()
    
    if (!this.states.has(key)) {
      const subscribers = new Set<() => void>()
      
      const setValue: StateUpdater<T> = (newValue) => {
        const hook = this.states.get(key)!
        const nextValue = typeof newValue === 'function' 
          ? (newValue as (prev: T) => T)(hook.value)
          : newValue
        
        if (nextValue !== hook.value) {
          hook.value = nextValue
          // Notify subscribers
          subscribers.forEach(callback => callback())
        }
      }

      this.states.set(key, {
        value: initialValue,
        setValue,
        subscribers
      })
    }

    const hook = this.states.get(key)!
    return [hook.value, hook.setValue]
  }

  /**
   * useEffect implementation
   */
  useEffect(callback: EffectCallback, dependencies?: DependencyList): void {
    const key = this.getHookKey()
    const existingEffect = this.effects.get(key)

    // Check if dependencies changed
    const depsChanged = !existingEffect || 
      !dependencies || 
      !existingEffect.dependencies ||
      dependencies.length !== existingEffect.dependencies.length ||
      dependencies.some((dep, i) => dep !== existingEffect.dependencies![i])

    if (depsChanged) {
      // Cleanup previous effect
      if (existingEffect?.cleanup) {
        existingEffect.cleanup()
      }

      // Run new effect
      const cleanup = callback()
      
      this.effects.set(key, {
        callback,
        dependencies,
        cleanup: cleanup || undefined
      })
    }
  }

  /**
   * Subscribe to state changes
   */
  subscribe(componentId: string, callback: () => void): () => void {
    const unsubscribers: (() => void)[] = []

    // Subscribe to all states for this component
    for (const [key, hook] of this.states.entries()) {
      if (key.startsWith(componentId + ':')) {
        hook.subscribers.add(callback)
        unsubscribers.push(() => hook.subscribers.delete(callback))
      }
    }

    // Return unsubscribe function
    return () => {
      unsubscribers.forEach(unsub => unsub())
    }
  }

  /**
   * Cleanup component
   */
  cleanup(componentId: string): void {
    // Cleanup effects
    for (const [key, effect] of this.effects.entries()) {
      if (key.startsWith(componentId + ':')) {
        if (effect.cleanup) {
          effect.cleanup()
        }
        this.effects.delete(key)
      }
    }

    // Remove states
    for (const key of this.states.keys()) {
      if (key.startsWith(componentId + ':')) {
        this.states.delete(key)
      }
    }
  }
}

// Global state manager instance
const stateManager = new KilatStateManager()

/**
 * useState hook
 */
export function useState<T>(initialValue: T): [T, StateUpdater<T>] {
  return stateManager.useState(initialValue)
}

/**
 * useEffect hook
 */
export function useEffect(callback: EffectCallback, dependencies?: DependencyList): void {
  return stateManager.useEffect(callback, dependencies)
}

/**
 * Custom hook for API calls
 */
export function useApi<T>(url: string, options?: RequestInit): {
  data: T | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
} {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch(url, options)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()
      setData(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [url])

  return { data, loading, error, refetch: fetchData }
}

/**
 * Custom hook for local storage
 */
export function useLocalStorage<T>(key: string, initialValue: T): [T, StateUpdater<T>] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      if (typeof window !== 'undefined') {
        const item = window.localStorage.getItem(key)
        return item ? JSON.parse(item) : initialValue
      }
      return initialValue
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error)
      return initialValue
    }
  })

  const setValue: StateUpdater<T> = (value) => {
    try {
      const valueToStore = typeof value === 'function' 
        ? (value as (prev: T) => T)(storedValue)
        : value
      
      setStoredValue(valueToStore)
      
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore))
      }
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error)
    }
  }

  return [storedValue, setValue]
}

/**
 * Custom hook for intervals
 */
export function useInterval(callback: () => void, delay: number | null): void {
  useEffect(() => {
    if (delay === null) return

    const interval = setInterval(callback, delay)
    return () => clearInterval(interval)
  }, [callback, delay])
}

/**
 * Custom hook for debounced values
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * Component wrapper for hook context
 */
export function withHooks<P extends object>(
  Component: (props: P) => any,
  componentId: string
) {
  return function WrappedComponent(props: P) {
    stateManager.setCurrentComponent(componentId)
    
    try {
      const result = Component(props)
      return result
    } finally {
      stateManager.clearCurrentComponent()
    }
  }
}

/**
 * Initialize component with hooks
 */
export function initializeComponent(componentId: string, renderFn: () => void): () => void {
  const unsubscribe = stateManager.subscribe(componentId, renderFn)
  
  return () => {
    unsubscribe()
    stateManager.cleanup(componentId)
  }
}

export { stateManager }
