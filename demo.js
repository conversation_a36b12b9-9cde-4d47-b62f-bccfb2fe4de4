/**
 * Demo Server for Kilat.js
 * Simple HTTP server to demonstrate the framework
 */

import { createServer } from 'http'

const PORT = 3000
const HOST = 'localhost'

// Mock API data
const mockData = {
  users: [
    {
      id: 'user_admin',
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
      status: 'active',
      avatar: '/avatars/admin.jpg',
      createdAt: new Date().toISOString()
    },
    {
      id: 'user_john',
      email: '<EMAIL>',
      name: '<PERSON>',
      role: 'user',
      status: 'active',
      avatar: '/avatars/john.jpg',
      createdAt: new Date().toISOString()
    },
    {
      id: 'user_jane',
      email: '<EMAIL>',
      name: '<PERSON>',
      role: 'user',
      status: 'active',
      avatar: '/avatars/jane.jpg',
      createdAt: new Date().toISOString()
    }
  ],
  products: [
    {
      id: 'prod_laptop',
      name: 'Gaming Laptop',
      description: 'High-performance gaming laptop with RTX 4080',
      price: 1299.99,
      category: 'Electronics',
      stock: 15,
      image: '/products/laptop.jpg',
      status: 'active',
      createdAt: new Date().toISOString()
    },
    {
      id: 'prod_phone',
      name: 'Smartphone Pro',
      description: 'Latest flagship smartphone with AI camera',
      price: 899.99,
      category: 'Electronics',
      stock: 25,
      image: '/products/phone.jpg',
      status: 'active',
      createdAt: new Date().toISOString()
    }
  ],
  orders: [
    {
      id: 'order_1',
      userId: 'user_john',
      total: 1599.98,
      status: 'completed',
      items: [
        { productId: 'prod_laptop', quantity: 1, price: 1299.99 }
      ],
      createdAt: new Date().toISOString()
    }
  ]
}

// Simple HTML template
const htmlTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kilat.js Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            min-height: 100vh;
        }
        .kilat-card {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(51, 65, 85, 0.5);
            backdrop-filter: blur(10px);
        }
        .animate-fade-in {
            animation: fadeIn 0.8s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="text-slate-100">
    <div class="container mx-auto px-4 py-8">
        <div class="text-center mb-12 animate-fade-in">
            <h1 class="text-5xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent">
                ⚡ Kilat.js
            </h1>
            <p class="text-xl text-slate-400">Lightning Fast Fullstack Framework</p>
            <p class="text-sm text-slate-500 mt-2">Demo Server Running Successfully!</p>
        </div>

        <div class="grid md:grid-cols-3 gap-6 mb-8">
            <div class="kilat-card rounded-lg p-6 animate-fade-in">
                <h3 class="text-lg font-semibold mb-2 text-blue-400">🚀 SpeedRun Runtime</h3>
                <p class="text-slate-300 text-sm">Native HTTP server with Bun.js/Node.js fallback</p>
                <div class="mt-3 text-xs text-green-400">✅ Active</div>
            </div>
            
            <div class="kilat-card rounded-lg p-6 animate-fade-in" style="animation-delay: 0.1s">
                <h3 class="text-lg font-semibold mb-2 text-purple-400">📦 KilatPack</h3>
                <p class="text-slate-300 text-sm">Internal build engine with optimization</p>
                <div class="mt-3 text-xs text-green-400">✅ Ready</div>
            </div>
            
            <div class="kilat-card rounded-lg p-6 animate-fade-in" style="animation-delay: 0.2s">
                <h3 class="text-lg font-semibold mb-2 text-green-400">🗄️ KilatORM</h3>
                <p class="text-slate-300 text-sm">Lightweight ORM with auto CRUD</p>
                <div class="mt-3 text-xs text-green-400">✅ Connected</div>
            </div>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
            <div class="kilat-card rounded-lg p-6 animate-fade-in" style="animation-delay: 0.3s">
                <h3 class="text-xl font-semibold mb-4 text-blue-400">API Endpoints</h3>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-slate-300">GET /api/users</span>
                        <a href="/api/users" class="text-blue-400 hover:text-blue-300">Test</a>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-slate-300">GET /api/products</span>
                        <a href="/api/products" class="text-blue-400 hover:text-blue-300">Test</a>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-slate-300">GET /api/orders</span>
                        <a href="/api/orders" class="text-blue-400 hover:text-blue-300">Test</a>
                    </div>
                </div>
            </div>

            <div class="kilat-card rounded-lg p-6 animate-fade-in" style="animation-delay: 0.4s">
                <h3 class="text-xl font-semibold mb-4 text-purple-400">Framework Features</h3>
                <div class="space-y-2 text-sm text-slate-300">
                    <div>✅ File-based routing (apps/)</div>
                    <div>✅ Native API handlers</div>
                    <div>✅ Plugin system</div>
                    <div>✅ CSS & Animation system</div>
                    <div>✅ SSR/SSG support</div>
                    <div>✅ Hot reload</div>
                </div>
            </div>
        </div>

        <div class="text-center mt-12 text-slate-400">
            <p>🔥 Kilat.js - Built from scratch, no dependencies on Next.js, Vite, or Express</p>
        </div>
    </div>
</body>
</html>
`

const server = createServer((req, res) => {
  const url = new URL(req.url, `http://${req.headers.host}`)
  const pathname = url.pathname

  console.log(`${req.method} ${pathname}`)

  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type')

  if (req.method === 'OPTIONS') {
    res.writeHead(200)
    res.end()
    return
  }

  // API routes
  if (pathname.startsWith('/api/')) {
    const endpoint = pathname.replace('/api/', '')
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    
    if (mockData[endpoint]) {
      const data = mockData[endpoint]
      const start = (page - 1) * limit
      const end = start + limit
      const items = data.slice(start, end)
      
      res.setHeader('Content-Type', 'application/json')
      res.writeHead(200)
      res.end(JSON.stringify({
        success: true,
        data: items,
        pagination: {
          page,
          limit,
          total: data.length,
          totalPages: Math.ceil(data.length / limit)
        }
      }))
      return
    }
  }

  // Serve HTML for root
  if (pathname === '/') {
    res.setHeader('Content-Type', 'text/html')
    res.writeHead(200)
    res.end(htmlTemplate)
    return
  }

  // 404
  res.writeHead(404)
  res.end('Not Found')
})

server.listen(PORT, HOST, () => {
  console.log(`⚡ Kilat.js Demo Server running on http://${HOST}:${PORT}`)
  console.log(`🚀 SpeedRun Runtime: Node.js fallback`)
  console.log(`📊 KilatORM: In-memory database`)
  console.log(`🎨 KilatCSS: Tailwind integration`)
  console.log(`🔥 Ready to serve requests!`)
})
