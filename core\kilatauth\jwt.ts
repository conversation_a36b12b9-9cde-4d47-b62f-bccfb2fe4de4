/**
 * KilatAuth - JWT Authentication System
 * Advanced JWT handling with refresh tokens and security features
 */

import { createHash, randomBytes, createHmac } from 'crypto'

export interface JWTPayload {
  sub: string // user id
  email: string
  role: string
  permissions: string[]
  iat: number
  exp: number
  jti: string // JWT ID for revocation
  type: 'access' | 'refresh'
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
  tokenType: 'Bearer'
}

export interface AuthUser {
  id: string
  email: string
  name: string
  role: string
  permissions: string[]
  lastLoginAt: Date
  isActive: boolean
}

export class KilatJWT {
  private secretKey: string
  private refreshSecretKey: string
  private accessTokenExpiry: number = 15 * 60 // 15 minutes
  private refreshTokenExpiry: number = 7 * 24 * 60 * 60 // 7 days
  private revokedTokens: Set<string> = new Set()

  constructor(secretKey?: string, refreshSecretKey?: string) {
    this.secretKey = secretKey || this.generateSecretKey()
    this.refreshSecretKey = refreshSecretKey || this.generateSecretKey()
  }

  /**
   * Generate secure secret key
   */
  private generateSecretKey(): string {
    return randomBytes(64).toString('hex')
  }

  /**
   * Create JWT token
   */
  private createToken(payload: any, secret: string, expiresIn: number): string {
    const header = {
      alg: 'HS256',
      typ: 'JWT'
    }

    const now = Math.floor(Date.now() / 1000)
    const tokenPayload = {
      ...payload,
      iat: now,
      exp: now + expiresIn,
      jti: randomBytes(16).toString('hex')
    }

    const encodedHeader = this.base64UrlEncode(JSON.stringify(header))
    const encodedPayload = this.base64UrlEncode(JSON.stringify(tokenPayload))
    
    const signature = this.createSignature(`${encodedHeader}.${encodedPayload}`, secret)
    
    return `${encodedHeader}.${encodedPayload}.${signature}`
  }

  /**
   * Verify JWT token
   */
  private verifyToken(token: string, secret: string): JWTPayload | null {
    try {
      const [header, payload, signature] = token.split('.')
      
      if (!header || !payload || !signature) {
        return null
      }

      // Verify signature
      const expectedSignature = this.createSignature(`${header}.${payload}`, secret)
      if (signature !== expectedSignature) {
        return null
      }

      const decodedPayload = JSON.parse(this.base64UrlDecode(payload)) as JWTPayload

      // Check expiration
      const now = Math.floor(Date.now() / 1000)
      if (decodedPayload.exp < now) {
        return null
      }

      // Check if token is revoked
      if (this.revokedTokens.has(decodedPayload.jti)) {
        return null
      }

      return decodedPayload
    } catch (error) {
      return null
    }
  }

  /**
   * Generate access and refresh tokens
   */
  generateTokens(user: AuthUser): AuthTokens {
    const payload = {
      sub: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions
    }

    const accessToken = this.createToken(
      { ...payload, type: 'access' },
      this.secretKey,
      this.accessTokenExpiry
    )

    const refreshToken = this.createToken(
      { ...payload, type: 'refresh' },
      this.refreshSecretKey,
      this.refreshTokenExpiry
    )

    return {
      accessToken,
      refreshToken,
      expiresIn: this.accessTokenExpiry,
      tokenType: 'Bearer'
    }
  }

  /**
   * Verify access token
   */
  verifyAccessToken(token: string): JWTPayload | null {
    const payload = this.verifyToken(token, this.secretKey)
    return payload?.type === 'access' ? payload : null
  }

  /**
   * Verify refresh token
   */
  verifyRefreshToken(token: string): JWTPayload | null {
    const payload = this.verifyToken(token, this.refreshSecretKey)
    return payload?.type === 'refresh' ? payload : null
  }

  /**
   * Refresh access token using refresh token
   */
  refreshAccessToken(refreshToken: string): string | null {
    const payload = this.verifyRefreshToken(refreshToken)
    if (!payload) return null

    const newAccessToken = this.createToken(
      {
        sub: payload.sub,
        email: payload.email,
        role: payload.role,
        permissions: payload.permissions,
        type: 'access'
      },
      this.secretKey,
      this.accessTokenExpiry
    )

    return newAccessToken
  }

  /**
   * Revoke token
   */
  revokeToken(token: string): boolean {
    try {
      const [, payload] = token.split('.')
      const decodedPayload = JSON.parse(this.base64UrlDecode(payload))
      this.revokedTokens.add(decodedPayload.jti)
      return true
    } catch {
      return false
    }
  }

  /**
   * Create HMAC signature
   */
  private createSignature(data: string, secret: string): string {
    return createHmac('sha256', secret)
      .update(data)
      .digest('base64url')
  }

  /**
   * Base64 URL encode
   */
  private base64UrlEncode(str: string): string {
    return Buffer.from(str)
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '')
  }

  /**
   * Base64 URL decode
   */
  private base64UrlDecode(str: string): string {
    str += '='.repeat((4 - str.length % 4) % 4)
    return Buffer.from(str.replace(/-/g, '+').replace(/_/g, '/'), 'base64').toString()
  }

  /**
   * Extract token from Authorization header
   */
  extractTokenFromHeader(authHeader: string): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null
    }
    return authHeader.substring(7)
  }

  /**
   * Generate secure password hash
   */
  hashPassword(password: string, salt?: string): { hash: string; salt: string } {
    const passwordSalt = salt || randomBytes(32).toString('hex')
    const hash = createHash('sha256')
      .update(password + passwordSalt)
      .digest('hex')
    
    return { hash, salt: passwordSalt }
  }

  /**
   * Verify password
   */
  verifyPassword(password: string, hash: string, salt: string): boolean {
    const { hash: computedHash } = this.hashPassword(password, salt)
    return computedHash === hash
  }
}
