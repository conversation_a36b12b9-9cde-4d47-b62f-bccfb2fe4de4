/**
 * UsersService - Business logic for users
 * Handles CRUD operations and business rules
 */

import { KilatORM } from '@/core/kilatorm'
import { generateId } from '@/core/kilatlib/utils'

interface User {
  id: string
  email: string
  name: string
  role: string
  passwordHash: string
  avatar?: string
  status: string
  lastLoginAt?: Date
  createdAt: Date
  updatedAt: Date
}

interface CreateUserData {
  email: string
  name: string
  password: string
  role?: string
  avatar?: string
}

interface UpdateUserData {
  email?: string
  name?: string
  role?: string
  avatar?: string
  status?: string
}

interface GetAllOptions {
  page: number
  limit: number
  search?: string
}

interface GetAllResult {
  items: User[]
  total: number
}

interface ServiceResult<T = any> {
  success: boolean
  data?: T
  error?: string
}

export class UsersService {
  private orm: KilatORM
  
  constructor() {
    this.orm = new KilatORM()
    // Initialize database connection
    this.orm.connect()
  }
  
  /**
   * Get all users with pagination
   */
  async getAll(options: GetAllOptions): Promise<GetAllResult> {
    try {
      const { page, limit, search } = options
      const offset = (page - 1) * limit
      
      let query: any = {}
      
      if (search) {
        query = {
          name: { $regex: search, $options: 'i' }
        }
      }
      
      const [items, total] = await Promise.all([
        this.orm.users.find(query)
          .skip(offset)
          .limit(limit)
          .sort({ createdAt: -1 }),
        this.orm.users.countDocuments(query)
      ])
      
      return { items, total }
      
    } catch (error) {
      console.error('Get all users error:', error)
      return { items: [], total: 0 }
    }
  }
  
  /**
   * Get user by ID
   */
  async getById(id: string): Promise<User | null> {
    try {
      return await this.orm.users.findById(id)
    } catch (error) {
      console.error('Get user by ID error:', error)
      return null
    }
  }
  
  /**
   * Create new user
   */
  async create(data: CreateUserData): Promise<ServiceResult> {
    try {
      // Hash password (simplified for demo)
      const passwordHash = `hashed_${data.password}`

      const user = await this.orm.users.create({
        id: generateId('user'),
        email: data.email,
        name: data.name,
        role: data.role || 'user',
        passwordHash,
        avatar: data.avatar || null,
        status: 'active',
        lastLoginAt: null,
        createdAt: new Date(),
        updatedAt: new Date()
      })

      return {
        success: true,
        data: user
      }

    } catch (error) {
      console.error('Create user error:', error)
      return {
        success: false,
        error: 'Failed to create user'
      }
    }
  }
  
  /**
   * Update user
   */
  async update(id: string, data: UpdateUserData): Promise<ServiceResult> {
    try {
      const existing = await this.orm.users.findById(id)

      if (!existing) {
        return {
          success: false,
          error: 'User not found'
        }
      }

      const updated = await this.orm.users.updateOne(
        { id },
        {
          ...data,
          updatedAt: new Date()
        }
      )

      return {
        success: true,
        data: updated
      }

    } catch (error) {
      console.error('Update user error:', error)
      return {
        success: false,
        error: 'Failed to update user'
      }
    }
  }
  
  /**
   * Delete user
   */
  async delete(id: string): Promise<ServiceResult> {
    try {
      const existing = await this.orm.users.findById(id)

      if (!existing) {
        return {
          success: false,
          error: 'User not found'
        }
      }

      await this.orm.users.deleteOne({ id })

      return {
        success: true
      }

    } catch (error) {
      console.error('Delete user error:', error)
      return {
        success: false,
        error: 'Failed to delete user'
      }
    }
  }
}
}
