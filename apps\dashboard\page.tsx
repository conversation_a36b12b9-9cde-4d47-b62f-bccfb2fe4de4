/**
 * Dashboard Overview Page
 * Route: /dashboard
 */

'use client'

import { KilatCard } from '@/components/ui/card'
import { useEffect, useState } from 'react'

interface DashboardStats {
  totalUsers: number
  totalProducts: number
  totalOrders: number
  revenue: number
}

interface RecentActivity {
  id: string
  type: string
  message: string
  timestamp: string
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalProducts: 0,
    totalOrders: 0,
    revenue: 0
  })
  const [activities, setActivities] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      // Fetch users count
      const usersResponse = await fetch('/api/users?limit=1')
      const usersData = await usersResponse.json()

      // Fetch products count
      const productsResponse = await fetch('/api/products?limit=1')
      const productsData = await productsResponse.json()

      // Fetch orders count
      const ordersResponse = await fetch('/api/orders?limit=1')
      const ordersData = await ordersResponse.json()

      setStats({
        totalUsers: usersData.pagination?.total || 0,
        totalProducts: productsData.pagination?.total || 0,
        totalOrders: ordersData.pagination?.total || 0,
        revenue: 12450.75 // Mock revenue for now
      })

      // Mock recent activities
      setActivities([
        {
          id: '1',
          type: 'user',
          message: 'New user registered',
          timestamp: '2 min ago'
        },
        {
          id: '2',
          type: 'product',
          message: 'Product inventory updated',
          timestamp: '5 min ago'
        },
        {
          id: '3',
          type: 'order',
          message: 'New order received',
          timestamp: '10 min ago'
        },
        {
          id: '4',
          type: 'system',
          message: 'Database backup completed',
          timestamp: '1 hour ago'
        }
      ])

    } catch (error) {
      console.error('Failed to fetch dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }
  return (
    <div className="space-y-8">
      <div className="animate-kilat-fade-in">
        <h1 className="text-3xl font-bold mb-2">Dashboard Overview</h1>
        <p className="text-glow-muted">Welcome to your Kilat.js dashboard</p>
      </div>

      {/* Stats Grid */}
      <div className="grid md:grid-cols-4 gap-6">
        <KilatCard className="animate-kilat-slide-up">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Users</p>
                <p className="text-2xl font-bold text-blue-400">
                  {loading ? '...' : stats.totalUsers.toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                👥
              </div>
            </div>
          </div>
        </KilatCard>

        <KilatCard className="animate-kilat-slide-up" style={{ animationDelay: '0.1s' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Products</p>
                <p className="text-2xl font-bold text-purple-400">
                  {loading ? '...' : stats.totalProducts.toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                📦
              </div>
            </div>
          </div>
        </KilatCard>

        <KilatCard className="animate-kilat-slide-up" style={{ animationDelay: '0.2s' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Total Orders</p>
                <p className="text-2xl font-bold text-green-400">
                  {loading ? '...' : stats.totalOrders.toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                🛒
              </div>
            </div>
          </div>
        </KilatCard>

        <KilatCard className="animate-kilat-slide-up" style={{ animationDelay: '0.3s' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-400">Revenue</p>
                <p className="text-2xl font-bold text-yellow-400">
                  ${loading ? '...' : stats.revenue.toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                💰
              </div>
            </div>
          </div>
        </KilatCard>
      </div>

      {/* Recent Activity */}
      <div className="grid md:grid-cols-2 gap-8">
        <KilatCard className="animate-kilat-scale-in">
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
            <div className="space-y-4">
              {loading ? (
                <div className="text-center text-slate-400">Loading activities...</div>
              ) : (
                activities.map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-3">
                    <div className={`w-2 h-2 rounded-full ${
                      activity.type === 'user' ? 'bg-blue-400' :
                      activity.type === 'product' ? 'bg-purple-400' :
                      activity.type === 'order' ? 'bg-green-400' :
                      'bg-yellow-400'
                    }`}></div>
                    <span className="text-sm text-slate-300">{activity.message}</span>
                    <span className="text-xs text-slate-400 ml-auto">{activity.timestamp}</span>
                  </div>
                ))
              )}
            </div>
          </div>
        </KilatCard>

        <KilatCard className="animate-kilat-scale-in" style={{ animationDelay: '0.1s' }}>
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">System Status</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">SpeedRun Runtime</span>
                <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">Online</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">KilatPack Build</span>
                <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">Ready</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Database</span>
                <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">Connected</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Cache Layer</span>
                <span className="text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded">Warming</span>
              </div>
            </div>
          </div>
        </KilatCard>
      </div>
    </div>
  )
}
