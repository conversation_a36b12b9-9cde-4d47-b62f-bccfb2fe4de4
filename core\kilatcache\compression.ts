/**
 * KilatCache - Compression Middleware
 * Advanced compression with multiple algorithms and smart selection
 */

import { gzipSync, deflateSync, brotliCompressSync } from 'zlib'

export interface CompressionOptions {
  threshold: number // Minimum size to compress (bytes)
  algorithms: CompressionAlgorithm[]
  level: number // Compression level (1-9)
  mimeTypes: string[]
  cacheCompressed: boolean
}

export type CompressionAlgorithm = 'gzip' | 'deflate' | 'br' | 'auto'

export interface CompressionResult {
  data: Buffer
  algorithm: string
  originalSize: number
  compressedSize: number
  ratio: number
  encoding: string
}

export class KilatCompression {
  private options: CompressionOptions
  private compressionCache: Map<string, CompressionResult> = new Map()

  constructor(options?: Partial<CompressionOptions>) {
    this.options = {
      threshold: 1024, // 1KB
      algorithms: ['br', 'gzip', 'deflate'],
      level: 6,
      mimeTypes: [
        'text/html',
        'text/css',
        'text/javascript',
        'application/javascript',
        'application/json',
        'text/xml',
        'application/xml',
        'text/plain',
        'image/svg+xml'
      ],
      cacheCompressed: true,
      ...options
    }
  }

  /**
   * Compress response data
   */
  compress(
    data: string | Buffer,
    acceptEncoding: string = '',
    contentType: string = 'text/plain'
  ): CompressionResult | null {
    const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data, 'utf8')
    
    // Check if compression is needed
    if (!this.shouldCompress(buffer, contentType)) {
      return null
    }

    // Generate cache key
    const cacheKey = this.generateCacheKey(buffer, acceptEncoding)
    
    // Check cache
    if (this.options.cacheCompressed && this.compressionCache.has(cacheKey)) {
      return this.compressionCache.get(cacheKey)!
    }

    // Determine best algorithm
    const algorithm = this.selectAlgorithm(acceptEncoding)
    if (!algorithm) return null

    // Compress data
    const result = this.compressWithAlgorithm(buffer, algorithm)
    
    // Cache result
    if (this.options.cacheCompressed && result) {
      this.compressionCache.set(cacheKey, result)
      
      // Limit cache size
      if (this.compressionCache.size > 1000) {
        const firstKey = this.compressionCache.keys().next().value
        this.compressionCache.delete(firstKey)
      }
    }

    return result
  }

  /**
   * Check if content should be compressed
   */
  private shouldCompress(data: Buffer, contentType: string): boolean {
    // Check size threshold
    if (data.length < this.options.threshold) {
      return false
    }

    // Check MIME type
    if (!this.options.mimeTypes.some(type => contentType.includes(type))) {
      return false
    }

    return true
  }

  /**
   * Select best compression algorithm
   */
  private selectAlgorithm(acceptEncoding: string): CompressionAlgorithm | null {
    const accepted = acceptEncoding.toLowerCase().split(',').map(s => s.trim())
    
    for (const algorithm of this.options.algorithms) {
      if (algorithm === 'auto') continue
      
      const encoding = this.getEncodingName(algorithm)
      if (accepted.some(enc => enc.includes(encoding))) {
        return algorithm
      }
    }

    return null
  }

  /**
   * Compress with specific algorithm
   */
  private compressWithAlgorithm(
    data: Buffer, 
    algorithm: CompressionAlgorithm
  ): CompressionResult | null {
    try {
      let compressed: Buffer
      let encoding: string

      switch (algorithm) {
        case 'gzip':
          compressed = gzipSync(data, { level: this.options.level })
          encoding = 'gzip'
          break
        case 'deflate':
          compressed = deflateSync(data, { level: this.options.level })
          encoding = 'deflate'
          break
        case 'br':
          compressed = brotliCompressSync(data, {
            params: {
              [require('zlib').constants.BROTLI_PARAM_QUALITY]: this.options.level
            }
          })
          encoding = 'br'
          break
        default:
          return null
      }

      const originalSize = data.length
      const compressedSize = compressed.length
      const ratio = ((originalSize - compressedSize) / originalSize) * 100

      // Only return if compression is beneficial
      if (compressedSize >= originalSize * 0.9) {
        return null
      }

      return {
        data: compressed,
        algorithm,
        originalSize,
        compressedSize,
        ratio,
        encoding
      }

    } catch (error) {
      console.error(`Compression error with ${algorithm}:`, error)
      return null
    }
  }

  /**
   * Get encoding name for algorithm
   */
  private getEncodingName(algorithm: CompressionAlgorithm): string {
    switch (algorithm) {
      case 'gzip': return 'gzip'
      case 'deflate': return 'deflate'
      case 'br': return 'br'
      default: return ''
    }
  }

  /**
   * Generate cache key
   */
  private generateCacheKey(data: Buffer, acceptEncoding: string): string {
    const hash = require('crypto')
      .createHash('md5')
      .update(data)
      .update(acceptEncoding)
      .digest('hex')
    return `comp_${hash}`
  }

  /**
   * Clear compression cache
   */
  clearCache(): void {
    this.compressionCache.clear()
  }

  /**
   * Get compression statistics
   */
  getStats(): {
    cacheSize: number
    totalCompressions: number
    averageRatio: number
  } {
    const results = Array.from(this.compressionCache.values())
    const totalRatio = results.reduce((sum, result) => sum + result.ratio, 0)
    
    return {
      cacheSize: this.compressionCache.size,
      totalCompressions: results.length,
      averageRatio: results.length > 0 ? totalRatio / results.length : 0
    }
  }

  /**
   * Update compression options
   */
  updateOptions(options: Partial<CompressionOptions>): void {
    this.options = { ...this.options, ...options }
  }
}

/**
 * Compression middleware for responses
 */
export class CompressionMiddleware {
  private compression: KilatCompression

  constructor(options?: Partial<CompressionOptions>) {
    this.compression = new KilatCompression(options)
  }

  /**
   * Process response for compression
   */
  async processResponse(
    response: Response,
    acceptEncoding: string = ''
  ): Promise<Response> {
    try {
      // Skip if already compressed
      if (response.headers.get('content-encoding')) {
        return response
      }

      const contentType = response.headers.get('content-type') || ''
      const body = await response.text()

      // Attempt compression
      const result = this.compression.compress(body, acceptEncoding, contentType)
      
      if (!result) {
        // Return original response
        return new Response(body, {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers
        })
      }

      // Create compressed response
      const headers = new Headers(response.headers)
      headers.set('content-encoding', result.encoding)
      headers.set('content-length', result.compressedSize.toString())
      headers.set('x-compression-ratio', result.ratio.toFixed(2))

      return new Response(result.data, {
        status: response.status,
        statusText: response.statusText,
        headers
      })

    } catch (error) {
      console.error('Compression middleware error:', error)
      return response
    }
  }

  /**
   * Get compression instance
   */
  getCompression(): KilatCompression {
    return this.compression
  }
}
