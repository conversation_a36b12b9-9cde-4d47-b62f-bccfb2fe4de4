#!/usr/bin/env bun

/**
 * Kilat.js CLI - Command line interface for Kilat.js framework
 * Provides development server, build tools, and generators
 */

import { SpeedRunRuntime } from './core/kilatcore/speedrun'
import { KilatStandaloneBuild } from './core/kilatbuild/standalone'
import { KilatGenerator } from './core/kilatlib/generator'
import { loadConfig } from './core/kilatcore/config'

const command = process.argv[2]
const args = process.argv.slice(3)

/**
 * Format bytes to human readable string
 */
function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

async function main() {
  console.log('⚡ Kilat.js CLI v1.0.0')



  switch (command) {
    case 'dev':
      await runDev()
      break

    case 'build':
      await runBuild()
      break

    case 'start':
      await runStart()
      break

    case 'export':
      await runExport()
      break

    case 'generate':
    case 'g':
      await runGenerate(args)
      break

    case 'upgrade':
      await runUpgrade()
      break

    case 'help':
    case '--help':
    case '-h':
    case undefined:
      showHelp()
      break

    default:
      console.error(`❌ Unknown command: ${command}`)
      showHelp()
      process.exit(1)
  }
}

/**
 * Run development server
 */
async function runDev() {
  console.log('🚀 Starting development server...')

  try {
    // Load config
    const config = await import('./kilat.config.js').then(m => m.default)

    // Set development environment
    process.env.NODE_ENV = 'development'

    // Import and start SpeedRun runtime
    const { SpeedRunRuntime } = await import('./core/kilatcore/speedrun')
    const runtime = new SpeedRunRuntime(config)

    // Initialize and start the server
    await runtime.initialize()
    await runtime.start()

    console.log('🔥 Hot Module Replacement enabled')
    console.log(`🌐 Server running at http://${config.runtime.host}:${config.runtime.port}`)
    console.log('✅ Development server ready!')

    // Keep the process alive
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down development server...')
      await runtime.stop()
      process.exit(0)
    })

  } catch (error) {
    console.error('❌ Failed to start development server:', error)
    process.exit(1)
  }
}

/**
 * Build for production
 */
async function runBuild() {
  console.log('📦 Building Kilat.js application...')

  try {
    // Load config
    const config = await import('./kilat.config.js').then(m => m.default)

    // Set production environment
    process.env.NODE_ENV = 'production'

    // Create standalone build configuration
    const buildConfig = {
      entry: './apps/page.tsx',
      outDir: './.kilat/dist',
      publicDir: './public',
      minify: true,
      sourcemap: true,
      target: 'es2022' as const,
      external: ['react', 'react-dom'],
      define: {
        'process.env.NODE_ENV': '"production"',
        '__DEV__': 'false'
      }
    }

    // Run standalone build
    const builder = new KilatStandaloneBuild(buildConfig)
    const result = await builder.build()

    if (result.success) {
      console.log('✅ Build completed successfully!')
      console.log(`📊 Build time: ${result.stats.duration}ms`)
      console.log(`📦 Total size: ${formatBytes(result.stats.totalSize)}`)
      console.log(`🗜️ Gzipped: ${formatBytes(result.stats.gzipSize)}`)
      console.log(`📄 Files: ${result.stats.files}`)

      if (result.warnings.length > 0) {
        console.log('⚠️ Warnings:')
        result.warnings.forEach(warning => console.log(`  - ${warning}`))
      }

      console.log(`\n📁 Output directory: ${buildConfig.outDir}`)
      console.log('🚀 Ready for deployment!')

    } else {
      console.error('❌ Build failed!')
      result.errors.forEach(error => console.error(`  - ${error}`))
      process.exit(1)
    }

  } catch (error) {
    console.error('❌ Build failed:', error)
    process.exit(1)
  }
}

/**
 * Start production server
 */
async function runStart() {
  console.log('🚀 Starting production server...')

  try {
    // Load config
    const config = await import('./kilat.config.js').then(m => m.default)

    // Set production environment
    process.env.NODE_ENV = 'production'

    // Check if build exists
    const buildDir = config.build.outDir
    const { stat } = await import('fs/promises')

    try {
      await stat(buildDir)
    } catch {
      console.error('❌ Build directory not found. Run "kilat build" first.')
      process.exit(1)
    }

    // Import and start SpeedRun runtime
    const { SpeedRunRuntime } = await import('./core/kilatcore/speedrun')
    const runtime = new SpeedRunRuntime(config)

    // Initialize and start the server
    await runtime.initialize()
    await runtime.start()

    console.log(`🌐 Production server running at http://${config.runtime.host}:${config.runtime.port}`)
    console.log('✅ Production server ready!')

    // Keep the process alive
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down production server...')
      await runtime.stop()
      process.exit(0)
    })

  } catch (error) {
    console.error('❌ Failed to start production server:', error)
    process.exit(1)
  }
}

/**
 * Export to static files
 */
async function runExport() {
  console.log('📤 Exporting to static files...')

  try {
    // Load config
    const config = await import('./kilat.config.js').then(m => m.default)

    // Set production environment
    process.env.NODE_ENV = 'production'

    // Import and run KilatPack export
    const { KilatPack } = await import('./core/kilatpack/builder')
    const builder = new KilatPack(config)

    // Run export process
    await builder.exportStatic()

    console.log('✅ Static export completed!')
    console.log(`📁 Files exported to: ${config.export.outDir}`)

  } catch (error) {
    console.error('❌ Export failed:', error)
    process.exit(1)
  }
}

/**
 * Generate code (pages, components, etc.)
 */
async function runGenerate(args: string[]) {
  const type = args[0]
  const name = args[1]

  if (!type || !name) {
    console.error('❌ Usage: kilat generate <type> <name>')
    console.log('Types: page, api, component, service, layout')
    process.exit(1)
  }

  try {
    // Load config
    const config = await import('./kilat.config.js').then(m => m.default)

    // Import generator
    const { Generator } = await import('./core/kilatlib/generator')
    const generator = new Generator(config)

    // Generate code
    await generator.generate(type, name, args.slice(2))
    console.log(`✅ Generated ${type}: ${name}`)

  } catch (error) {
    console.error(`❌ Failed to generate ${type}:`, error)
    process.exit(1)
  }
}

/**
 * Upgrade Kilat.js
 */
async function runUpgrade() {
  console.log('⬆️ Upgrading Kilat.js...')

  try {
    // Check current version
    const packageJson = await import('./package.json')
    const currentVersion = packageJson.version
    console.log(`📦 Current version: ${currentVersion}`)

    // In a real implementation, this would:
    // 1. Check for latest version from registry
    // 2. Download and install updates
    // 3. Run migration scripts if needed
    // 4. Update dependencies

    console.log('🔍 Checking for updates...')

    // Simulate version check
    await new Promise(resolve => setTimeout(resolve, 1000))

    console.log('✅ Kilat.js is up to date!')
    console.log('💡 To check for updates manually, visit: https://kilat.js.org/releases')

  } catch (error) {
    console.error('❌ Upgrade failed:', error)
    process.exit(1)
  }
}

/**
 * Show help information
 */
function showHelp() {
  console.log(`
⚡ Kilat.js CLI Commands:

Development:
  kilat dev              Start development server with hot reload
  kilat build            Build for production
  kilat start            Start production server
  kilat export           Export to static files (SSG)

Generators:
  kilat generate page <name>        Generate new page
  kilat generate api <name>         Generate API route
  kilat generate component <name>   Generate component
  kilat generate service <name>     Generate service
  kilat generate layout <name>      Generate layout

Utilities:
  kilat upgrade          Upgrade Kilat.js to latest version
  kilat help             Show this help message

Examples:
  kilat dev                         # Start dev server
  kilat generate page about         # Create apps/about/page.tsx
  kilat generate api users          # Create apps/api/users/route.ts
  kilat generate component Button   # Create components/ui/Button.tsx
  kilat build                       # Build for production
  kilat export                      # Export static site

For more information, visit: https://kilat.js.org
`)
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('\n👋 Goodbye!')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n👋 Goodbye!')
  process.exit(0)
})

// Run the CLI
main().catch(error => {
  console.error('❌ CLI Error:', error)
  process.exit(1)
})
