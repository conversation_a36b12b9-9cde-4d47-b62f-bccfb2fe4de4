/**
 * KilatCache - Advanced Caching System
 * Multi-layer caching with TTL, compression, and invalidation
 */

import { createHash } from 'crypto'
import { gzipSync, gunzipSync } from 'zlib'

export interface CacheOptions {
  ttl?: number // Time to live in milliseconds
  compress?: boolean
  tags?: string[]
  priority?: 'low' | 'normal' | 'high'
  maxSize?: number // Max size in bytes
}

export interface CacheEntry {
  key: string
  value: any
  compressed: boolean
  size: number
  createdAt: number
  expiresAt: number
  accessCount: number
  lastAccessed: number
  tags: string[]
  priority: 'low' | 'normal' | 'high'
}

export interface CacheStats {
  hits: number
  misses: number
  sets: number
  deletes: number
  evictions: number
  totalSize: number
  entryCount: number
  hitRate: number
}

export class KilatCache {
  private cache: Map<string, CacheEntry> = new Map()
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    evictions: 0,
    totalSize: 0,
    entryCount: 0,
    hitRate: 0
  }
  private maxMemory: number
  private cleanupInterval: NodeJS.Timeout | null = null

  constructor(maxMemory: number = 100 * 1024 * 1024) { // 100MB default
    this.maxMemory = maxMemory
    this.startCleanupTimer()
  }

  /**
   * Set cache entry
   */
  set(key: string, value: any, options: CacheOptions = {}): boolean {
    try {
      const {
        ttl = 60 * 60 * 1000, // 1 hour default
        compress = false,
        tags = [],
        priority = 'normal',
        maxSize = 1024 * 1024 // 1MB default
      } = options

      let serializedValue = JSON.stringify(value)
      let finalValue = serializedValue
      let compressed = false
      let size = Buffer.byteLength(serializedValue, 'utf8')

      // Compress if enabled and beneficial
      if (compress && size > 1024) { // Only compress if > 1KB
        const compressedBuffer = gzipSync(Buffer.from(serializedValue, 'utf8'))
        if (compressedBuffer.length < size * 0.8) { // Only if compression saves 20%+
          finalValue = compressedBuffer.toString('base64')
          size = compressedBuffer.length
          compressed = true
        }
      }

      // Check size limit
      if (size > maxSize) {
        console.warn(`Cache entry too large: ${key} (${size} bytes)`)
        return false
      }

      // Ensure we have space
      this.ensureSpace(size)

      const now = Date.now()
      const entry: CacheEntry = {
        key,
        value: finalValue,
        compressed,
        size,
        createdAt: now,
        expiresAt: now + ttl,
        accessCount: 0,
        lastAccessed: now,
        tags,
        priority
      }

      // Remove existing entry if present
      if (this.cache.has(key)) {
        const existing = this.cache.get(key)!
        this.stats.totalSize -= existing.size
      }

      this.cache.set(key, entry)
      this.stats.totalSize += size
      this.stats.entryCount = this.cache.size
      this.stats.sets++

      this.updateHitRate()
      return true

    } catch (error) {
      console.error('Cache set error:', error)
      return false
    }
  }

  /**
   * Get cache entry
   */
  get(key: string): any | null {
    const entry = this.cache.get(key)

    if (!entry) {
      this.stats.misses++
      this.updateHitRate()
      return null
    }

    // Check expiration
    if (Date.now() > entry.expiresAt) {
      this.delete(key)
      this.stats.misses++
      this.updateHitRate()
      return null
    }

    // Update access stats
    entry.accessCount++
    entry.lastAccessed = Date.now()

    this.stats.hits++
    this.updateHitRate()

    try {
      // Decompress if needed
      let value = entry.value
      if (entry.compressed) {
        const buffer = Buffer.from(value, 'base64')
        value = gunzipSync(buffer).toString('utf8')
      }

      return JSON.parse(value)
    } catch (error) {
      console.error('Cache get error:', error)
      this.delete(key)
      return null
    }
  }

  /**
   * Delete cache entry
   */
  delete(key: string): boolean {
    const entry = this.cache.get(key)
    if (entry) {
      this.cache.delete(key)
      this.stats.totalSize -= entry.size
      this.stats.entryCount = this.cache.size
      this.stats.deletes++
      return true
    }
    return false
  }

  /**
   * Check if key exists
   */
  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false

    // Check expiration
    if (Date.now() > entry.expiresAt) {
      this.delete(key)
      return false
    }

    return true
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear()
    this.stats.totalSize = 0
    this.stats.entryCount = 0
  }

  /**
   * Invalidate by tags
   */
  invalidateByTags(tags: string[]): number {
    let count = 0
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags.some(tag => tags.includes(tag))) {
        this.delete(key)
        count++
      }
    }
    return count
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats }
  }

  /**
   * Get cache keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys())
  }

  /**
   * Get cache size
   */
  size(): number {
    return this.cache.size
  }

  /**
   * Get memory usage
   */
  getMemoryUsage(): { used: number; max: number; percentage: number } {
    return {
      used: this.stats.totalSize,
      max: this.maxMemory,
      percentage: (this.stats.totalSize / this.maxMemory) * 100
    }
  }

  /**
   * Ensure we have enough space
   */
  private ensureSpace(requiredSize: number): void {
    if (this.stats.totalSize + requiredSize <= this.maxMemory) {
      return
    }

    // Evict entries using LRU with priority consideration
    const entries = Array.from(this.cache.entries())
      .map(([key, entry]) => ({ key, entry }))
      .sort((a, b) => {
        // Sort by priority first, then by last accessed
        const priorityOrder = { low: 0, normal: 1, high: 2 }
        const priorityDiff = priorityOrder[a.entry.priority] - priorityOrder[b.entry.priority]
        if (priorityDiff !== 0) return priorityDiff
        return a.entry.lastAccessed - b.entry.lastAccessed
      })

    for (const { key } of entries) {
      this.delete(key)
      this.stats.evictions++
      
      if (this.stats.totalSize + requiredSize <= this.maxMemory) {
        break
      }
    }
  }

  /**
   * Update hit rate
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0
  }

  /**
   * Start cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 5 * 60 * 1000) // Every 5 minutes
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const now = Date.now()
    let cleaned = 0

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.delete(key)
        cleaned++
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 Cache cleanup: removed ${cleaned} expired entries`)
    }
  }

  /**
   * Generate cache key
   */
  static generateKey(prefix: string, ...parts: any[]): string {
    const combined = parts.map(part => 
      typeof part === 'object' ? JSON.stringify(part) : String(part)
    ).join(':')
    
    const hash = createHash('md5').update(combined).digest('hex')
    return `${prefix}:${hash}`
  }

  /**
   * Destroy cache
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    this.clear()
  }
}
