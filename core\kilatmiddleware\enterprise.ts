/**
 * KilatMiddleware Enterprise - Production-Grade Middleware System
 * Advanced middleware with error boundaries, monitoring, and security
 */

import { KilatPerformanceMonitor } from '../kilatcache/performance'
import { KilatAuthService } from '../kilatauth/auth.service'
import { CompressionMiddleware } from '../kilatcache/compression'

export interface MiddlewareContext {
  request: Request
  response?: Response
  user?: any
  session?: any
  startTime: number
  requestId: string
  metadata: Record<string, any>
}

export interface MiddlewareResult {
  continue: boolean
  response?: Response
  error?: Error
  metadata?: Record<string, any>
}

export interface MiddlewareConfig {
  enabled: boolean
  order: number
  skipPaths?: string[]
  onlyPaths?: string[]
  options?: Record<string, any>
}

export abstract class BaseMiddleware {
  protected config: MiddlewareConfig
  protected name: string

  constructor(name: string, config: Partial<MiddlewareConfig> = {}) {
    this.name = name
    this.config = {
      enabled: true,
      order: 100,
      skipPaths: [],
      onlyPaths: [],
      options: {},
      ...config
    }
  }

  abstract execute(context: MiddlewareContext): Promise<MiddlewareResult>

  shouldExecute(context: MiddlewareContext): boolean {
    if (!this.config.enabled) return false

    const url = new URL(context.request.url)
    const pathname = url.pathname

    // Check skip paths
    if (this.config.skipPaths?.some(path => pathname.startsWith(path))) {
      return false
    }

    // Check only paths
    if (this.config.onlyPaths?.length && !this.config.onlyPaths.some(path => pathname.startsWith(path))) {
      return false
    }

    return true
  }
}

/**
 * Request Logging Middleware
 */
export class RequestLoggingMiddleware extends BaseMiddleware {
  constructor(config?: Partial<MiddlewareConfig>) {
    super('RequestLogging', { order: 1, ...config })
  }

  async execute(context: MiddlewareContext): Promise<MiddlewareResult> {
    const url = new URL(context.request.url)
    const method = context.request.method
    const userAgent = context.request.headers.get('user-agent') || 'unknown'
    const ip = context.request.headers.get('x-forwarded-for') || 
              context.request.headers.get('x-real-ip') || 'unknown'

    console.log(`📝 ${method} ${url.pathname} - ${ip} - ${userAgent} [${context.requestId}]`)

    context.metadata.loggedAt = new Date()
    context.metadata.ip = ip
    context.metadata.userAgent = userAgent

    return { continue: true }
  }
}

/**
 * Security Headers Middleware
 */
export class SecurityHeadersMiddleware extends BaseMiddleware {
  constructor(config?: Partial<MiddlewareConfig>) {
    super('SecurityHeaders', { order: 10, ...config })
  }

  async execute(context: MiddlewareContext): Promise<MiddlewareResult> {
    const headers = new Headers()

    // Security headers
    headers.set('X-Content-Type-Options', 'nosniff')
    headers.set('X-Frame-Options', 'DENY')
    headers.set('X-XSS-Protection', '1; mode=block')
    headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
    headers.set('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com; style-src 'self' 'unsafe-inline'")
    headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
    headers.set('X-DNS-Prefetch-Control', 'off')
    headers.set('X-Download-Options', 'noopen')
    headers.set('X-Permitted-Cross-Domain-Policies', 'none')

    // CORS headers
    headers.set('Access-Control-Allow-Origin', '*')
    headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
    headers.set('Access-Control-Max-Age', '86400')

    context.metadata.securityHeaders = Object.fromEntries(headers.entries())

    return { continue: true }
  }
}

/**
 * Rate Limiting Middleware
 */
export class RateLimitingMiddleware extends BaseMiddleware {
  private requests: Map<string, { count: number; resetTime: number }> = new Map()

  constructor(config?: Partial<MiddlewareConfig>) {
    super('RateLimiting', { 
      order: 20, 
      options: { 
        windowMs: 15 * 60 * 1000, // 15 minutes
        maxRequests: 100 
      },
      ...config 
    })

    // Cleanup expired entries every minute
    setInterval(() => this.cleanup(), 60000)
  }

  async execute(context: MiddlewareContext): Promise<MiddlewareResult> {
    const ip = context.metadata.ip || 'unknown'
    const now = Date.now()
    const windowMs = this.config.options!.windowMs
    const maxRequests = this.config.options!.maxRequests

    let entry = this.requests.get(ip)
    
    if (!entry || entry.resetTime <= now) {
      entry = {
        count: 1,
        resetTime: now + windowMs
      }
      this.requests.set(ip, entry)
    } else {
      entry.count++
    }

    const remaining = Math.max(0, maxRequests - entry.count)
    const resetTime = Math.ceil((entry.resetTime - now) / 1000)

    context.metadata.rateLimit = {
      limit: maxRequests,
      remaining,
      resetTime,
      exceeded: entry.count > maxRequests
    }

    if (entry.count > maxRequests) {
      const response = new Response(
        JSON.stringify({
          error: 'Rate limit exceeded',
          retryAfter: resetTime
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': resetTime.toString(),
            'X-RateLimit-Limit': maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': entry.resetTime.toString()
          }
        }
      )

      return { continue: false, response }
    }

    return { continue: true }
  }

  private cleanup(): void {
    const now = Date.now()
    for (const [ip, entry] of this.requests.entries()) {
      if (entry.resetTime <= now) {
        this.requests.delete(ip)
      }
    }
  }
}

/**
 * Authentication Middleware
 */
export class AuthenticationMiddleware extends BaseMiddleware {
  private authService: KilatAuthService

  constructor(config?: Partial<MiddlewareConfig>) {
    super('Authentication', { 
      order: 30,
      skipPaths: ['/api/auth/login', '/api/auth/register', '/api/auth/refresh', '/'],
      ...config 
    })
    this.authService = new KilatAuthService()
  }

  async execute(context: MiddlewareContext): Promise<MiddlewareResult> {
    const authHeader = context.request.headers.get('Authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      const response = new Response(
        JSON.stringify({ error: 'Authentication required' }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
            'WWW-Authenticate': 'Bearer'
          }
        }
      )
      return { continue: false, response }
    }

    const token = authHeader.substring(7)
    const user = await this.authService.verifyToken(token)

    if (!user) {
      const response = new Response(
        JSON.stringify({ error: 'Invalid token' }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
            'WWW-Authenticate': 'Bearer'
          }
        }
      )
      return { continue: false, response }
    }

    context.user = user
    context.metadata.authenticated = true
    context.metadata.userId = user.id

    return { continue: true }
  }
}

/**
 * Error Boundary Middleware
 */
export class ErrorBoundaryMiddleware extends BaseMiddleware {
  constructor(config?: Partial<MiddlewareConfig>) {
    super('ErrorBoundary', { order: 1000, ...config })
  }

  async execute(context: MiddlewareContext): Promise<MiddlewareResult> {
    try {
      // This middleware wraps the entire request processing
      return { continue: true }
    } catch (error) {
      console.error('🚨 Unhandled error:', error)

      const isDevelopment = process.env.NODE_ENV === 'development'
      const errorResponse = {
        error: 'Internal Server Error',
        requestId: context.requestId,
        timestamp: new Date().toISOString(),
        ...(isDevelopment && { 
          stack: error instanceof Error ? error.stack : undefined,
          message: error instanceof Error ? error.message : 'Unknown error'
        })
      }

      const response = new Response(
        JSON.stringify(errorResponse),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            'X-Request-ID': context.requestId
          }
        }
      )

      return { continue: false, response, error: error instanceof Error ? error : new Error('Unknown error') }
    }
  }
}

/**
 * Performance Monitoring Middleware
 */
export class PerformanceMiddleware extends BaseMiddleware {
  private monitor: KilatPerformanceMonitor

  constructor(config?: Partial<MiddlewareConfig>) {
    super('Performance', { order: 5, ...config })
    this.monitor = new KilatPerformanceMonitor()
  }

  async execute(context: MiddlewareContext): Promise<MiddlewareResult> {
    const url = new URL(context.request.url)
    const requestId = this.monitor.startRequest(context.request.method, url.pathname)
    
    context.metadata.performanceId = requestId
    context.metadata.startTime = Date.now()

    return { continue: true }
  }

  async finalize(context: MiddlewareContext, response: Response): Promise<void> {
    if (context.metadata.performanceId) {
      const responseSize = response.headers.get('content-length')
      
      this.monitor.endRequest(
        context.metadata.performanceId,
        response.status,
        responseSize ? parseInt(responseSize) : undefined,
        context.metadata.cacheHit,
        context.metadata.dbQueries,
        context.metadata.errors
      )
    }
  }
}

/**
 * Compression Middleware
 */
export class CompressionEnterpriseMiddleware extends BaseMiddleware {
  private compression: CompressionMiddleware

  constructor(config?: Partial<MiddlewareConfig>) {
    super('Compression', { order: 900, ...config })
    this.compression = new CompressionMiddleware({
      threshold: 1024,
      algorithms: ['br', 'gzip', 'deflate']
    })
  }

  async execute(context: MiddlewareContext): Promise<MiddlewareResult> {
    // Compression is applied to responses, not requests
    return { continue: true }
  }

  async processResponse(response: Response, context: MiddlewareContext): Promise<Response> {
    const acceptEncoding = context.request.headers.get('accept-encoding') || ''
    return await this.compression.processResponse(response, acceptEncoding)
  }
}

/**
 * Enterprise Middleware Manager
 */
export class KilatMiddlewareManager {
  private middlewares: BaseMiddleware[] = []
  private performanceMiddleware?: PerformanceMiddleware

  constructor() {
    this.initializeDefaultMiddlewares()
  }

  private initializeDefaultMiddlewares(): void {
    // Add default middlewares in order
    this.addMiddleware(new RequestLoggingMiddleware())
    this.addMiddleware(new SecurityHeadersMiddleware())
    this.addMiddleware(new RateLimitingMiddleware())
    this.addMiddleware(new AuthenticationMiddleware())
    
    this.performanceMiddleware = new PerformanceMiddleware()
    this.addMiddleware(this.performanceMiddleware)
    
    this.addMiddleware(new CompressionEnterpriseMiddleware())
    this.addMiddleware(new ErrorBoundaryMiddleware())
  }

  addMiddleware(middleware: BaseMiddleware): void {
    this.middlewares.push(middleware)
    this.middlewares.sort((a, b) => a.config.order - b.config.order)
  }

  async processRequest(request: Request): Promise<Response> {
    const context: MiddlewareContext = {
      request,
      startTime: Date.now(),
      requestId: this.generateRequestId(),
      metadata: {}
    }

    try {
      // Execute middlewares in order
      for (const middleware of this.middlewares) {
        if (!middleware.shouldExecute(context)) continue

        const result = await middleware.execute(context)
        
        if (!result.continue) {
          if (result.response) {
            return await this.finalizeResponse(result.response, context)
          }
          if (result.error) {
            throw result.error
          }
        }

        if (result.metadata) {
          Object.assign(context.metadata, result.metadata)
        }
      }

      // If we get here, no middleware returned a response
      // This should be handled by the router
      const response = new Response('Not Found', { status: 404 })
      return await this.finalizeResponse(response, context)

    } catch (error) {
      console.error('Middleware error:', error)
      const errorResponse = new Response(
        JSON.stringify({ error: 'Internal Server Error' }),
        { 
          status: 500, 
          headers: { 'Content-Type': 'application/json' }
        }
      )
      return await this.finalizeResponse(errorResponse, context)
    }
  }

  private async finalizeResponse(response: Response, context: MiddlewareContext): Promise<Response> {
    // Apply response processing middlewares
    let finalResponse = response

    for (const middleware of this.middlewares) {
      if (middleware instanceof CompressionEnterpriseMiddleware) {
        finalResponse = await middleware.processResponse(finalResponse, context)
      }
    }

    // Finalize performance monitoring
    if (this.performanceMiddleware) {
      await this.performanceMiddleware.finalize(context, finalResponse)
    }

    // Add metadata headers
    const headers = new Headers(finalResponse.headers)
    headers.set('X-Request-ID', context.requestId)
    headers.set('X-Response-Time', `${Date.now() - context.startTime}ms`)

    if (context.metadata.securityHeaders) {
      Object.entries(context.metadata.securityHeaders).forEach(([key, value]) => {
        headers.set(key, value as string)
      })
    }

    return new Response(finalResponse.body, {
      status: finalResponse.status,
      statusText: finalResponse.statusText,
      headers
    })
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`
  }

  getStats(): any {
    return {
      middlewareCount: this.middlewares.length,
      middlewares: this.middlewares.map(m => ({
        name: m.name,
        enabled: m.config.enabled,
        order: m.config.order
      }))
    }
  }
}
