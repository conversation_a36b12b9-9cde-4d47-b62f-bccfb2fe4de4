/**
 * Kilat.js Universal Demo Server
 * Showcasing SSR, SSG, CSR, ISR capabilities
 * Enterprise-grade framework demonstration
 */

import { createServer } from 'http'
import { readFileSync, existsSync } from 'fs'
import { join, extname } from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const PORT = 3000
const HOST = 'localhost'

// Import universal systems
let KilatORMEnterprise, KilatUniversalRenderer, KilatWebSocketServer, KilatSSEServer

try {
  const ormModule = await import('./core/kilatorm/enterprise.js')
  const renderModule = await import('./core/kilatrender/universal.js')
  const wsModule = await import('./core/kilatrealtime/websocket.js')
  const sseModule = await import('./core/kilatrealtime/sse.js')
  
  KilatORMEnterprise = ormModule.KilatORMEnterprise
  KilatUniversalRenderer = renderModule.KilatUniversalRenderer
  KilatWebSocketServer = wsModule.KilatWebSocketServer
  KilatSSEServer = sseModule.KilatSSEServer
} catch (error) {
  console.warn('⚠️ Universal modules not available, using fallback')
}

// Initialize enterprise systems
const orm = KilatORMEnterprise ? new KilatORMEnterprise({ type: 'memory' }) : null
const renderer = KilatUniversalRenderer ? new KilatUniversalRenderer({
  mode: 'ssr',
  cache: { enabled: true, ttl: 60000 },
  prerender: { enabled: true, routes: ['/'] },
  streaming: { enabled: true, suspense: true }
}) : null

const wsServer = KilatWebSocketServer ? new KilatWebSocketServer({ port: 3001 }) : null
const sseServer = KilatSSEServer ? new KilatSSEServer() : null

// Initialize systems
if (orm) await orm.connect()
if (wsServer) await wsServer.start()

// Request statistics
const stats = {
  total: 0,
  byMode: new Map(),
  byPath: new Map(),
  errors: 0,
  startTime: Date.now(),
  renderTimes: []
}

const server = createServer(async (req, res) => {
  const startTime = Date.now()
  const url = new URL(req.url, `http://${req.headers.host}`)
  const pathname = url.pathname
  const method = req.method

  // Update statistics
  stats.total++
  stats.byPath.set(pathname, (stats.byPath.get(pathname) || 0) + 1)

  console.log(`${method} ${pathname} - ${new Date().toISOString()}`)

  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Render-Mode')

  if (method === 'OPTIONS') {
    res.writeHead(200)
    res.end()
    return
  }

  try {
    // Server-Sent Events endpoint
    if (pathname === '/api/sse') {
      if (sseServer) {
        const connectionId = sseServer.handleConnection(req, res)
        
        // Auto-subscribe to stats channel
        setTimeout(() => {
          sseServer.subscribe(connectionId, 'stats')
          sseServer.subscribe(connectionId, 'system')
        }, 100)
        
        return
      }
    }

    // API Routes with real data
    if (pathname.startsWith('/api/')) {
      await handleAPIRoute(pathname, method, url, req, res)
      return
    }

    // Universal Rendering Routes
    if (renderer) {
      await handleUniversalRoute(pathname, req, res)
      return
    }

    // Fallback static demo
    handleFallbackDemo(pathname, res)

  } catch (error) {
    console.error('Request error:', error)
    stats.errors++
    
    res.writeHead(500, { 'Content-Type': 'application/json' })
    res.end(JSON.stringify({ error: 'Internal Server Error' }))
  }

  const duration = Date.now() - startTime
  stats.renderTimes.push(duration)
  console.log(`  → ${res.statusCode} (${duration}ms)`)
})

/**
 * Handle API routes with real data
 */
async function handleAPIRoute(pathname, method, url, req, res) {
  const endpoint = pathname.replace('/api/', '')
  
  if (endpoint === 'users') {
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    
    if (orm) {
      const users = await orm.query('SELECT * FROM users ORDER BY created_at DESC LIMIT ? OFFSET ?', [limit, (page - 1) * limit])
      const total = await orm.query('SELECT COUNT(*) as count FROM users')
      
      res.setHeader('Content-Type', 'application/json')
      res.writeHead(200)
      res.end(JSON.stringify({
        success: true,
        data: users.rows,
        pagination: {
          page,
          limit,
          total: total.rows[0]?.count || 0,
          totalPages: Math.ceil((total.rows[0]?.count || 0) / limit)
        }
      }))
    } else {
      // Fallback data
      res.setHeader('Content-Type', 'application/json')
      res.writeHead(200)
      res.end(JSON.stringify({
        success: true,
        data: generateFallbackUsers(limit),
        pagination: { page, limit, total: 100, totalPages: 10 }
      }))
    }
    return
  }

  if (endpoint === 'system/stats') {
    const systemStats = {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      requests: stats.total,
      errors: stats.errors,
      avgResponseTime: stats.renderTimes.length > 0 
        ? stats.renderTimes.reduce((a, b) => a + b, 0) / stats.renderTimes.length 
        : 0,
      renderModes: Object.fromEntries(stats.byMode),
      topPaths: Array.from(stats.byPath.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([path, count]) => ({ path, count })),
      realtime: {
        websocket: wsServer ? wsServer.getStats() : null,
        sse: sseServer ? sseServer.getStats() : null
      }
    }

    res.setHeader('Content-Type', 'application/json')
    res.writeHead(200)
    res.end(JSON.stringify({
      success: true,
      data: systemStats
    }))
    return
  }

  // Unknown API endpoint
  res.writeHead(404, { 'Content-Type': 'application/json' })
  res.end(JSON.stringify({ error: 'API endpoint not found' }))
}

/**
 * Handle universal rendering routes
 */
async function handleUniversalRoute(pathname, req, res) {
  const renderMode = req.headers['x-render-mode'] || 'ssr'
  
  const context = {
    url: req.url,
    method: req.method,
    headers: req.headers,
    cookies: {},
    query: Object.fromEntries(new URL(req.url, `http://${req.headers.host}`).searchParams),
    params: {}
  }

  try {
    const result = await renderer.render(pathname, context)
    
    // Update stats
    stats.byMode.set(result.performance.mode, (stats.byMode.get(result.performance.mode) || 0) + 1)
    
    // Set headers
    res.setHeader('Content-Type', 'text/html')
    res.setHeader('X-Render-Mode', result.performance.mode)
    res.setHeader('X-Render-Time', result.performance.renderTime.toString())
    res.setHeader('X-Data-Fetch-Time', result.performance.dataFetchTime.toString())
    res.setHeader('X-Cache-Hit', result.performance.cacheHit.toString())
    
    if (result.revalidate) {
      res.setHeader('X-Revalidate', result.revalidate.toString())
    }

    res.writeHead(200)
    
    const fullHTML = `
      <!DOCTYPE html>
      <html lang="en">
      ${result.head}
      <body>
        ${result.html}
        
        <script>
          window.__KILAT_INITIAL_PROPS__ = ${JSON.stringify(result.data)};
          window.__KILAT_MODE__ = '${result.performance.mode}';
          window.__KILAT_REALTIME_ENABLED__ = true;
          window.__KILAT_PERFORMANCE__ = ${JSON.stringify(result.performance)};
        </script>
        
        ${result.scripts.map(script => `<script src="${script}"></script>`).join('\n')}
      </body>
      </html>
    `
    
    res.end(fullHTML)

  } catch (error) {
    console.error('Universal render error:', error)
    res.writeHead(500)
    res.end(`
      <!DOCTYPE html>
      <html>
      <head><title>Render Error</title></head>
      <body>
        <h1>Render Error</h1>
        <p>${error.message}</p>
      </body>
      </html>
    `)
  }
}

/**
 * Handle fallback demo
 */
function handleFallbackDemo(pathname, res) {
  const demoHTML = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Kilat.js Universal Framework Demo</title>
        <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body class="bg-slate-900 text-white min-h-screen">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center mb-12">
                <div class="inline-flex items-center justify-center w-24 h-24 mb-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 animate-pulse">
                    <span class="text-4xl font-bold">⚡</span>
                </div>
                
                <h1 class="text-6xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent">
                    Kilat.js Universal
                </h1>
                
                <p class="text-xl text-slate-400 mb-8">
                    Enterprise Full-Stack Framework with Universal Rendering
                </p>
            </div>

            <!-- Rendering Modes -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
                <div class="bg-slate-800 p-6 rounded-lg border border-slate-700 text-center">
                    <div class="text-3xl mb-3">🖥️</div>
                    <h3 class="text-lg font-bold text-blue-400 mb-2">SSR</h3>
                    <p class="text-sm text-slate-400">Server-Side Rendering</p>
                    <p class="text-xs text-slate-500 mt-2">Real-time data, SEO optimized</p>
                </div>
                
                <div class="bg-slate-800 p-6 rounded-lg border border-slate-700 text-center">
                    <div class="text-3xl mb-3">⚡</div>
                    <h3 class="text-lg font-bold text-green-400 mb-2">SSG</h3>
                    <p class="text-sm text-slate-400">Static Site Generation</p>
                    <p class="text-xs text-slate-500 mt-2">Pre-built, CDN ready</p>
                </div>
                
                <div class="bg-slate-800 p-6 rounded-lg border border-slate-700 text-center">
                    <div class="text-3xl mb-3">🌐</div>
                    <h3 class="text-lg font-bold text-purple-400 mb-2">CSR</h3>
                    <p class="text-sm text-slate-400">Client-Side Rendering</p>
                    <p class="text-xs text-slate-500 mt-2">SPA experience</p>
                </div>
                
                <div class="bg-slate-800 p-6 rounded-lg border border-slate-700 text-center">
                    <div class="text-3xl mb-3">🔄</div>
                    <h3 class="text-lg font-bold text-cyan-400 mb-2">ISR</h3>
                    <p class="text-sm text-slate-400">Incremental Static Regen</p>
                    <p class="text-xs text-slate-500 mt-2">Best of both worlds</p>
                </div>
            </div>

            <!-- Enterprise Features -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div class="bg-slate-800 p-6 rounded-lg border border-slate-700">
                    <h3 class="text-lg font-bold mb-4 text-blue-400">🗄️ Enterprise Database</h3>
                    <ul class="text-sm text-slate-300 space-y-1">
                        <li>✅ Built-in ORM</li>
                        <li>✅ Migrations & Relations</li>
                        <li>✅ SQLite/PostgreSQL</li>
                        <li>✅ Real-time queries</li>
                    </ul>
                </div>
                
                <div class="bg-slate-800 p-6 rounded-lg border border-slate-700">
                    <h3 class="text-lg font-bold mb-4 text-green-400">🔄 Real-time Features</h3>
                    <ul class="text-sm text-slate-300 space-y-1">
                        <li>✅ WebSocket Server</li>
                        <li>✅ Server-Sent Events</li>
                        <li>✅ Live Updates</li>
                        <li>✅ Channel Management</li>
                    </ul>
                </div>
                
                <div class="bg-slate-800 p-6 rounded-lg border border-slate-700">
                    <h3 class="text-lg font-bold mb-4 text-purple-400">🚀 Performance</h3>
                    <ul class="text-sm text-slate-300 space-y-1">
                        <li>✅ Zero Dependencies</li>
                        <li>✅ Advanced Caching</li>
                        <li>✅ Code Splitting</li>
                        <li>✅ Tree Shaking</li>
                    </ul>
                </div>
            </div>

            <!-- Live Stats -->
            <div class="bg-slate-800 p-6 rounded-lg border border-slate-700 text-center">
                <h3 class="text-lg font-bold mb-4 text-cyan-400">📊 Live Server Stats</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                        <div class="text-2xl font-bold text-blue-400">${stats.total}</div>
                        <div class="text-xs text-slate-400">Total Requests</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-green-400">${Math.floor(process.uptime() / 60)}m</div>
                        <div class="text-xs text-slate-400">Uptime</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-purple-400">${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB</div>
                        <div class="text-xs text-slate-400">Memory</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-cyan-400">${stats.errors}</div>
                        <div class="text-xs text-slate-400">Errors</div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
  `

  res.setHeader('Content-Type', 'text/html')
  res.writeHead(200)
  res.end(demoHTML)
}

/**
 * Generate fallback users
 */
function generateFallbackUsers(count) {
  const users = []
  const names = ['John Doe', 'Jane Smith', 'Mike Johnson', 'Sarah Wilson', 'David Brown']
  
  for (let i = 0; i < count; i++) {
    users.push({
      id: `user_${i + 1}`,
      name: names[i % names.length],
      email: `user${i + 1}@example.com`,
      role: i === 0 ? 'admin' : 'user',
      status: 'active',
      created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
    })
  }
  
  return users
}

// Broadcast system stats every 5 seconds
if (sseServer) {
  setInterval(() => {
    sseServer.broadcastSystemStats()
  }, 5000)
}

server.listen(PORT, HOST, () => {
  console.log(`⚡ Kilat.js Universal Demo Server`)
  console.log(`🚀 Running on http://${HOST}:${PORT}`)
  console.log(`🎭 Rendering modes: SSR ✅ SSG ✅ CSR ✅ ISR ✅`)
  console.log(`🗄️ Database: ${orm ? 'Connected' : 'Fallback mode'}`)
  console.log(`🔄 WebSocket: ${wsServer ? `ws://localhost:3001` : 'Fallback mode'}`)
  console.log(`📡 Server-Sent Events: ${sseServer ? 'Enabled' : 'Fallback mode'}`)
  console.log(`🌟 Enterprise features: All systems operational!`)
  console.log(`✅ Ready to demonstrate universal rendering capabilities!`)
})
