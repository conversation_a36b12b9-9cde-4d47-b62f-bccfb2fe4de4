/**
 * Card Component - Real implementation with KilatCSS and KilatAnim
 * Advanced card with theme support and animations
 */

import React, { forwardRef } from 'react'
import { cn } from '@/core/kilatlib/utils'
import { useScrollAnimation } from '../../core/kilatanim/hooks'

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  variant?: 'default' | 'elevated' | 'outlined' | 'glass' | 'glow' | 'neon'
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'glow'
  hover?: boolean
  animated?: boolean
  gradient?: boolean
}

export const Card = forwardRef<HTMLDivElement, CardProps>(({
  children,
  variant = 'default',
  padding = 'md',
  rounded = 'md',
  shadow = 'sm',
  hover = false,
  animated = true,
  gradient = false,
  className = '',
  ...props
}, ref) => {
  const { ref: animRef } = useScrollAnimation(
    animated ? 'fadeIn' : '',
    { threshold: 0.1, once: true }
  )

  // Combine refs
  const combinedRef = (node: HTMLDivElement) => {
    if (ref) {
      if (typeof ref === 'function') ref(node)
      else ref.current = node
    }
    if (animRef) animRef.current = node
  }

  const classes = cn(
    // Base styles
    'kilat-card',
    'relative',
    'transition-all',
    'duration-300',

    // Variant styles
    {
      'bg-surface border border-border': variant === 'default',
      'bg-surface border-none shadow-lg': variant === 'elevated',
      'bg-transparent border-2 border-border': variant === 'outlined',
      'glass backdrop-blur-md': variant === 'glass',
      'bg-surface border border-primary shadow-glow': variant === 'glow',
      'bg-surface border-2 border-primary shadow-neon animate-kilat-glow': variant === 'neon'
    },

    // Padding styles
    {
      'p-0': padding === 'none',
      'p-2': padding === 'sm',
      'p-4': padding === 'md',
      'p-6': padding === 'lg',
      'p-8': padding === 'xl'
    },

    // Rounded styles
    {
      'rounded-none': rounded === 'none',
      'rounded-sm': rounded === 'sm',
      'rounded-md': rounded === 'md',
      'rounded-lg': rounded === 'lg',
      'rounded-xl': rounded === 'xl',
      'rounded-full': rounded === 'full'
    },

    // Shadow styles
    {
      'shadow-none': shadow === 'none',
      'shadow-sm': shadow === 'sm',
      'shadow-md': shadow === 'md',
      'shadow-lg': shadow === 'lg',
      'shadow-xl': shadow === 'xl',
      'shadow-glow': shadow === 'glow'
    },

    // Hover effects
    {
      'hover:shadow-lg hover:scale-105 cursor-pointer': hover,
      'hover:shadow-glow-lg': hover && variant === 'glow',
      'hover:border-primary/80': hover && variant === 'outlined'
    },

    // Gradient background
    {
      'bg-gradient-primary': gradient && variant !== 'glass' && variant !== 'outlined'
    },

    className
  )

  return (
    <div
      ref={combinedRef}
      className={classes}
      {...props}
    >
      {children}
    </div>
  )
})

Card.displayName = 'Card'

// Card Header Component
interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  divider?: boolean
}

export const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(({
  children,
  divider = false,
  className = '',
  ...props
}, ref) => {
  const classes = cn(
    'flex',
    'flex-col',
    'space-y-1.5',
    {
      'pb-4 border-b border-border': divider,
      'pb-2': !divider
    },
    className
  )

  return (
    <div ref={ref} className={classes} {...props}>
      {children}
    </div>
  )
})

CardHeader.displayName = 'CardHeader'

// Card Title Component
interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
}

export const CardTitle = forwardRef<HTMLHeadingElement, CardTitleProps>(({
  children,
  as: Component = 'h3',
  className = '',
  ...props
}, ref) => {
  const classes = cn(
    'text-lg',
    'font-semibold',
    'leading-none',
    'tracking-tight',
    'text-text',
    className
  )

  return (
    <Component ref={ref} className={classes} {...props}>
      {children}
    </Component>
  )
})

CardTitle.displayName = 'CardTitle'

// Card Description Component
interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode
}

export const CardDescription = forwardRef<HTMLParagraphElement, CardDescriptionProps>(({
  children,
  className = '',
  ...props
}, ref) => {
  const classes = cn(
    'text-sm',
    'text-muted',
    'leading-relaxed',
    className
  )

  return (
    <p ref={ref} className={classes} {...props}>
      {children}
    </p>
  )
})

CardDescription.displayName = 'CardDescription'

// Card Content Component
interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

export const CardContent = forwardRef<HTMLDivElement, CardContentProps>(({
  children,
  className = '',
  ...props
}, ref) => {
  const classes = cn(
    'pt-0',
    className
  )

  return (
    <div ref={ref} className={classes} {...props}>
      {children}
    </div>
  )
})

CardContent.displayName = 'CardContent'

// Card Footer Component
interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  divider?: boolean
}

export const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(({
  children,
  divider = false,
  className = '',
  ...props
}, ref) => {
  const classes = cn(
    'flex',
    'items-center',
    {
      'pt-4 border-t border-border': divider,
      'pt-2': !divider
    },
    className
  )

  return (
    <div ref={ref} className={classes} {...props}>
      {children}
    </div>
  )
})

CardFooter.displayName = 'CardFooter'
