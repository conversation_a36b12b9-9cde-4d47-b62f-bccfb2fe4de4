/**
 * Card - Reusable component
 */

import { ReactNode } from 'react'
import { cn } from '@/core/kilatlib/utils'

interface CardProps {
  children?: ReactNode
  className?: string
  variant?: 'default' | 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
}

export function Card({
  children,
  className,
  variant = 'default',
  size = 'md',
  ...props
}: CardProps) {
  return (
    <div
      className={cn(
        'kilat-card',
        // Base styles
        'rounded-lg transition-all duration-200',
        // Variant styles
        {
          'bg-glow-surface border border-glow-surface': variant === 'default',
          'bg-glow-primary text-white': variant === 'primary',
          'bg-glow-secondary text-white': variant === 'secondary',
        },
        // Size styles
        {
          'p-2 text-sm': size === 'sm',
          'p-4': size === 'md',
          'p-6 text-lg': size === 'lg',
        },
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

// Export alias for easier imports
export const KilatCard = Card
