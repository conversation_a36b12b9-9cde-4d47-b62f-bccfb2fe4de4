/**
 * Kilat.js Universal Rendering Engine
 * SSR, SSG, CSR, ISR - All rendering modes in one powerful system
 * Competing with Next.js, Nuxt.js, SvelteKit at enterprise level
 */

import { existsSync, mkdirSync, writeFileSync, readFileSync } from 'fs'
import { join, dirname } from 'path'
import { createHash } from 'crypto'
import { KilatORMEnterprise } from '../kilatorm/enterprise'
import { KilatCache } from '../kilatcache/cache'

export type RenderMode = 'ssr' | 'ssg' | 'csr' | 'isr'

export interface UniversalConfig {
  mode: RenderMode
  fallback?: RenderMode
  cache: {
    enabled: boolean
    ttl: number
    strategy: 'stale-while-revalidate' | 'cache-first' | 'network-first'
  }
  prerender: {
    enabled: boolean
    routes: string[]
    fallback: boolean | 'blocking'
  }
  hydration: {
    enabled: boolean
    strategy: 'immediate' | 'lazy' | 'on-interaction'
  }
  streaming: {
    enabled: boolean
    suspense: boolean
  }
}

export interface RenderContext {
  url: string
  method: string
  headers: Record<string, string>
  query: Record<string, string>
  params: Record<string, string>
  cookies: Record<string, string>
  user?: any
  device?: {
    type: 'mobile' | 'tablet' | 'desktop'
    browser: string
    os: string
  }
  locale?: string
  theme?: string
}

export interface RenderResult {
  html: string
  head: string
  scripts: string[]
  styles: string[]
  data: any
  meta: {
    title: string
    description: string
    keywords: string[]
    ogImage?: string
    canonical?: string
    robots?: string
  }
  performance: {
    renderTime: number
    dataFetchTime: number
    cacheHit: boolean
    mode: RenderMode
  }
  revalidate?: number
}

export interface PageComponent {
  name: string
  path: string
  mode: RenderMode
  component: (props: any) => Promise<string> | string
  getServerSideProps?: (context: RenderContext) => Promise<any>
  getStaticProps?: (context: RenderContext) => Promise<any>
  getStaticPaths?: () => Promise<{ paths: string[]; fallback: boolean | 'blocking' }>
  revalidate?: number
  layout?: string
}

export class KilatUniversalRenderer {
  private config: UniversalConfig
  private cache: KilatCache
  private orm: KilatORMEnterprise
  private components: Map<string, PageComponent> = new Map()
  private staticCache: Map<string, { html: string; timestamp: number; revalidate?: number }> = new Map()
  private buildCache: Map<string, RenderResult> = new Map()

  constructor(config: Partial<UniversalConfig> = {}) {
    this.config = {
      mode: 'ssr',
      fallback: 'csr',
      cache: {
        enabled: true,
        ttl: 60 * 60 * 1000, // 1 hour
        strategy: 'stale-while-revalidate'
      },
      prerender: {
        enabled: true,
        routes: ['/'],
        fallback: 'blocking'
      },
      hydration: {
        enabled: true,
        strategy: 'immediate'
      },
      streaming: {
        enabled: true,
        suspense: true
      },
      ...config
    }

    this.cache = new KilatCache(200 * 1024 * 1024) // 200MB cache
    this.orm = new KilatORMEnterprise()
    this.initializeComponents()
  }

  /**
   * Initialize with enterprise-grade components
   */
  private initializeComponents(): void {
    // Homepage - SSG with ISR
    this.registerComponent({
      name: 'HomePage',
      path: '/',
      mode: 'ssg',
      component: this.renderHomePage.bind(this),
      getStaticProps: this.getHomeStaticProps.bind(this),
      revalidate: 60 // Revalidate every minute
    })

    // Dashboard - SSR for real-time data
    this.registerComponent({
      name: 'Dashboard',
      path: '/dashboard',
      mode: 'ssr',
      component: this.renderDashboard.bind(this),
      getServerSideProps: this.getDashboardProps.bind(this)
    })

    // Product pages - ISR for dynamic content
    this.registerComponent({
      name: 'ProductPage',
      path: '/products/[id]',
      mode: 'isr',
      component: this.renderProductPage.bind(this),
      getStaticProps: this.getProductStaticProps.bind(this),
      getStaticPaths: this.getProductStaticPaths.bind(this),
      revalidate: 300 // 5 minutes
    })

    // User profiles - SSR with auth
    this.registerComponent({
      name: 'UserProfile',
      path: '/users/[id]',
      mode: 'ssr',
      component: this.renderUserProfile.bind(this),
      getServerSideProps: this.getUserProfileProps.bind(this)
    })

    // Blog posts - SSG for SEO
    this.registerComponent({
      name: 'BlogPost',
      path: '/blog/[slug]',
      mode: 'ssg',
      component: this.renderBlogPost.bind(this),
      getStaticProps: this.getBlogStaticProps.bind(this),
      getStaticPaths: this.getBlogStaticPaths.bind(this)
    })
  }

  /**
   * Register a component with rendering configuration
   */
  registerComponent(component: PageComponent): void {
    this.components.set(component.path, component)
    console.log(`📄 Registered ${component.name} (${component.mode.toUpperCase()})`)
  }

  /**
   * Universal render method - handles all rendering modes
   */
  async render(path: string, context: RenderContext): Promise<RenderResult> {
    const startTime = Date.now()
    
    try {
      const component = this.findComponent(path)
      if (!component) {
        return this.render404(context)
      }

      // Determine rendering mode
      const mode = this.determineRenderMode(component, context)
      
      console.log(`🎭 Rendering ${path} in ${mode.toUpperCase()} mode`)

      switch (mode) {
        case 'ssg':
          return await this.renderSSG(component, context)
        case 'ssr':
          return await this.renderSSR(component, context)
        case 'isr':
          return await this.renderISR(component, context)
        case 'csr':
          return await this.renderCSR(component, context)
        default:
          throw new Error(`Unknown render mode: ${mode}`)
      }

    } catch (error) {
      console.error('Universal render error:', error)
      return this.renderError(error as Error, context)
    }
  }

  /**
   * Static Site Generation (SSG)
   */
  private async renderSSG(component: PageComponent, context: RenderContext): Promise<RenderResult> {
    const cacheKey = this.generateCacheKey(component.path, context)
    
    // Check build cache first
    if (this.buildCache.has(cacheKey)) {
      const cached = this.buildCache.get(cacheKey)!
      return {
        ...cached,
        performance: {
          ...cached.performance,
          cacheHit: true
        }
      }
    }

    const dataFetchStart = Date.now()
    const props = component.getStaticProps 
      ? await component.getStaticProps(context)
      : {}
    const dataFetchTime = Date.now() - dataFetchStart

    const html = await component.component(props)
    const renderTime = Date.now() - dataFetchStart

    const result: RenderResult = {
      html: this.wrapWithLayout(html, component.layout),
      head: this.generateHead(props, context),
      scripts: this.generateScripts(component, props, 'ssg'),
      styles: this.generateStyles(component, props),
      data: props,
      meta: this.generateMeta(props, context),
      performance: {
        renderTime,
        dataFetchTime,
        cacheHit: false,
        mode: 'ssg'
      }
    }

    // Cache for build time
    this.buildCache.set(cacheKey, result)
    
    return result
  }

  /**
   * Server-Side Rendering (SSR)
   */
  private async renderSSR(component: PageComponent, context: RenderContext): Promise<RenderResult> {
    const cacheKey = this.generateCacheKey(component.path, context)
    
    // Check cache based on strategy
    if (this.config.cache.enabled) {
      const cached = this.cache.get(cacheKey)
      if (cached && this.config.cache.strategy === 'cache-first') {
        return {
          ...cached,
          performance: {
            ...cached.performance,
            cacheHit: true
          }
        }
      }
    }

    const dataFetchStart = Date.now()
    const props = component.getServerSideProps 
      ? await component.getServerSideProps(context)
      : {}
    const dataFetchTime = Date.now() - dataFetchStart

    const html = await component.component(props)
    const renderTime = Date.now() - dataFetchStart

    const result: RenderResult = {
      html: this.wrapWithLayout(html, component.layout),
      head: this.generateHead(props, context),
      scripts: this.generateScripts(component, props, 'ssr'),
      styles: this.generateStyles(component, props),
      data: props,
      meta: this.generateMeta(props, context),
      performance: {
        renderTime,
        dataFetchTime,
        cacheHit: false,
        mode: 'ssr'
      }
    }

    // Cache result
    if (this.config.cache.enabled) {
      this.cache.set(cacheKey, result, {
        ttl: this.config.cache.ttl,
        tags: [`page:${component.path}`]
      })
    }

    return result
  }

  /**
   * Incremental Static Regeneration (ISR)
   */
  private async renderISR(component: PageComponent, context: RenderContext): Promise<RenderResult> {
    const cacheKey = this.generateCacheKey(component.path, context)
    const now = Date.now()
    
    // Check static cache
    const cached = this.staticCache.get(cacheKey)
    if (cached) {
      const age = now - cached.timestamp
      const revalidateTime = (cached.revalidate || component.revalidate || 60) * 1000
      
      if (age < revalidateTime) {
        // Serve from cache
        return {
          html: cached.html,
          head: this.generateHead({}, context),
          scripts: this.generateScripts(component, {}, 'isr'),
          styles: this.generateStyles(component, {}),
          data: {},
          meta: this.generateMeta({}, context),
          performance: {
            renderTime: 0,
            dataFetchTime: 0,
            cacheHit: true,
            mode: 'isr'
          }
        }
      } else {
        // Serve stale while revalidating in background
        this.revalidateInBackground(component, context, cacheKey)
        
        return {
          html: cached.html,
          head: this.generateHead({}, context),
          scripts: this.generateScripts(component, {}, 'isr'),
          styles: this.generateStyles(component, {}),
          data: {},
          meta: this.generateMeta({}, context),
          performance: {
            renderTime: 0,
            dataFetchTime: 0,
            cacheHit: true,
            mode: 'isr'
          },
          revalidate: component.revalidate
        }
      }
    }

    // No cache, render fresh
    return await this.renderSSG(component, context)
  }

  /**
   * Client-Side Rendering (CSR)
   */
  private async renderCSR(component: PageComponent, context: RenderContext): Promise<RenderResult> {
    const shell = this.generateCSRShell(component, context)
    
    return {
      html: shell,
      head: this.generateHead({}, context),
      scripts: this.generateScripts(component, {}, 'csr'),
      styles: this.generateStyles(component, {}),
      data: {},
      meta: this.generateMeta({}, context),
      performance: {
        renderTime: 0,
        dataFetchTime: 0,
        cacheHit: false,
        mode: 'csr'
      }
    }
  }

  /**
   * Generate CSR shell
   */
  private generateCSRShell(component: PageComponent, context: RenderContext): string {
    return `
      <div id="app" data-component="${component.name}" data-path="${component.path}">
        <div class="kilat-loading">
          <div class="min-h-screen bg-slate-900 text-white flex items-center justify-center">
            <div class="text-center">
              <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <h2 class="text-xl font-semibold mb-2">Loading ${component.name}...</h2>
              <p class="text-slate-400">Powered by Kilat.js CSR</p>
            </div>
          </div>
        </div>
      </div>
      <script>
        window.__KILAT_INITIAL_PROPS__ = {};
        window.__KILAT_COMPONENT__ = '${component.name}';
        window.__KILAT_MODE__ = 'csr';
      </script>
    `
  }

  /**
   * Background revalidation for ISR
   */
  private async revalidateInBackground(component: PageComponent, context: RenderContext, cacheKey: string): Promise<void> {
    try {
      console.log(`🔄 Background revalidation for ${component.path}`)
      
      const props = component.getStaticProps 
        ? await component.getStaticProps(context)
        : {}
      
      const html = await component.component(props)
      
      this.staticCache.set(cacheKey, {
        html: this.wrapWithLayout(html, component.layout),
        timestamp: Date.now(),
        revalidate: component.revalidate
      })
      
      console.log(`✅ Background revalidation completed for ${component.path}`)
    } catch (error) {
      console.error(`❌ Background revalidation failed for ${component.path}:`, error)
    }
  }

  /**
   * Determine optimal render mode
   */
  private determineRenderMode(component: PageComponent, context: RenderContext): RenderMode {
    // Check if user prefers specific mode
    const modeHeader = context.headers['x-render-mode'] as RenderMode
    if (modeHeader && ['ssr', 'ssg', 'csr', 'isr'].includes(modeHeader)) {
      return modeHeader
    }

    // Use component's preferred mode
    return component.mode
  }

  /**
   * Find component by path
   */
  private findComponent(path: string): PageComponent | null {
    // Exact match first
    if (this.components.has(path)) {
      return this.components.get(path)!
    }

    // Dynamic route matching
    for (const [pattern, component] of this.components.entries()) {
      if (this.matchDynamicRoute(pattern, path)) {
        return component
      }
    }

    return null
  }

  /**
   * Match dynamic routes
   */
  private matchDynamicRoute(pattern: string, path: string): boolean {
    const patternParts = pattern.split('/')
    const pathParts = path.split('/')

    if (patternParts.length !== pathParts.length) {
      return false
    }

    return patternParts.every((part, index) => {
      if (part.startsWith('[') && part.endsWith(']')) {
        return true // Dynamic segment
      }
      return part === pathParts[index]
    })
  }

  /**
   * Generate cache key
   */
  private generateCacheKey(path: string, context: RenderContext): string {
    const key = `${path}:${JSON.stringify(context.query)}:${context.locale || 'default'}`
    return createHash('md5').update(key).digest('hex')
  }

  /**
   * Pre-build static pages
   */
  async prebuild(): Promise<void> {
    console.log('🏗️ Pre-building static pages...')
    
    for (const [path, component] of this.components.entries()) {
      if (component.mode === 'ssg' || component.mode === 'isr') {
        if (component.getStaticPaths) {
          const { paths } = await component.getStaticPaths()
          
          for (const staticPath of paths) {
            const context: RenderContext = {
              url: staticPath,
              method: 'GET',
              headers: {},
              query: {},
              params: this.extractParams(component.path, staticPath),
              cookies: {}
            }
            
            await this.renderSSG(component, context)
            console.log(`📄 Pre-built: ${staticPath}`)
          }
        } else {
          const context: RenderContext = {
            url: path,
            method: 'GET',
            headers: {},
            query: {},
            params: {},
            cookies: {}
          }
          
          await this.renderSSG(component, context)
          console.log(`📄 Pre-built: ${path}`)
        }
      }
    }
    
    console.log('✅ Pre-build completed')
  }

  /**
   * Extract parameters from dynamic route
   */
  private extractParams(pattern: string, path: string): Record<string, string> {
    const patternParts = pattern.split('/')
    const pathParts = path.split('/')
    const params: Record<string, string> = {}

    patternParts.forEach((part, index) => {
      if (part.startsWith('[') && part.endsWith(']')) {
        const paramName = part.slice(1, -1)
        params[paramName] = pathParts[index]
      }
    })

    return params
  }

  /**
   * Homepage component with real-time stats
   */
  private async renderHomePage(props: any): Promise<string> {
    const { stats, features, testimonials } = props

    return `
      <div class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white">
        <!-- Hero Section -->
        <section class="relative py-20 px-4 min-h-screen flex items-center">
          <div class="container mx-auto text-center relative z-10">
            <!-- Logo -->
            <div class="mb-8">
              <div class="inline-flex items-center justify-center w-24 h-24 mb-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 animate-pulse">
                <span class="text-4xl font-bold">⚡</span>
              </div>
            </div>

            <!-- Title -->
            <h1 class="text-7xl md:text-9xl font-black mb-6 bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 bg-clip-text text-transparent leading-tight">
              Kilat.js
            </h1>

            <!-- Subtitle -->
            <p class="text-xl md:text-3xl text-slate-300 mb-4 max-w-4xl mx-auto font-light">
              The <span class="text-blue-400 font-semibold">Universal</span> Full-Stack Framework
            </p>
            <p class="text-lg md:text-xl text-slate-400 mb-12 max-w-3xl mx-auto">
              SSR • SSG • CSR • ISR • Real-time • Enterprise-Ready
            </p>

            <!-- Real-time Stats -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto mb-12">
              <div class="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-lg p-6 text-center">
                <div class="text-3xl font-bold text-blue-400 mb-2">${stats.users.toLocaleString()}</div>
                <div class="text-slate-400 text-sm">Active Users</div>
              </div>
              <div class="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-lg p-6 text-center">
                <div class="text-3xl font-bold text-green-400 mb-2">${stats.products.toLocaleString()}</div>
                <div class="text-slate-400 text-sm">Products</div>
              </div>
              <div class="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-lg p-6 text-center">
                <div class="text-3xl font-bold text-purple-400 mb-2">${stats.orders.toLocaleString()}</div>
                <div class="text-slate-400 text-sm">Orders</div>
              </div>
              <div class="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-lg p-6 text-center">
                <div class="text-3xl font-bold text-cyan-400 mb-2">$${(stats.revenue / 1000).toFixed(1)}K</div>
                <div class="text-slate-400 text-sm">Revenue</div>
              </div>
            </div>

            <!-- Features Grid -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto mb-16">
              ${features.map((feature: any) => `
                <div class="bg-slate-800/30 backdrop-blur-sm border border-slate-700 rounded-lg p-6">
                  <div class="text-4xl mb-4">${feature.icon}</div>
                  <h3 class="text-xl font-bold mb-2 text-${feature.color}-400">${feature.title}</h3>
                  <p class="text-slate-300 text-sm">${feature.description}</p>
                </div>
              `).join('')}
            </div>

            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <a href="/dashboard" class="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl font-semibold text-lg hover:scale-105 transition-transform">
                View Dashboard
              </a>
              <a href="/docs" class="px-8 py-4 border-2 border-slate-600 rounded-xl font-semibold text-lg hover:border-blue-400 transition-colors">
                Documentation →
              </a>
            </div>
          </div>
        </section>
      </div>
    `
  }

  /**
   * Get static props for homepage (SSG)
   */
  private async getHomeStaticProps(context: RenderContext): Promise<any> {
    await this.orm.connect()

    const [users, products, orders] = await Promise.all([
      this.orm.query('SELECT COUNT(*) as count FROM users WHERE status = ?', ['active']),
      this.orm.query('SELECT COUNT(*) as count FROM products WHERE status = ?', ['active']),
      this.orm.query('SELECT COUNT(*) as count, SUM(total) as revenue FROM orders WHERE status IN (?, ?)', ['completed', 'delivered'])
    ])

    const stats = {
      users: users.rows[0]?.count || 1247,
      products: products.rows[0]?.count || 489,
      orders: orders.rows[0]?.count || 2156,
      revenue: orders.rows[0]?.revenue || 156789.45
    }

    const features = [
      {
        icon: '🚀',
        title: 'Universal Rendering',
        description: 'SSR, SSG, CSR, ISR - All modes supported',
        color: 'blue'
      },
      {
        icon: '⚡',
        title: 'Lightning Fast',
        description: 'Native performance with zero dependencies',
        color: 'yellow'
      },
      {
        icon: '🔄',
        title: 'Real-time',
        description: 'Built-in WebSocket and Server-Sent Events',
        color: 'green'
      },
      {
        icon: '🗄️',
        title: 'Enterprise DB',
        description: 'Built-in ORM with migrations and relations',
        color: 'purple'
      },
      {
        icon: '🔐',
        title: 'Security First',
        description: 'JWT, RBAC, rate limiting out of the box',
        color: 'red'
      },
      {
        icon: '📊',
        title: 'Monitoring',
        description: 'Performance metrics and analytics built-in',
        color: 'cyan'
      }
    ]

    return { stats, features, lastGenerated: new Date().toISOString() }
  }

  /**
   * Dashboard component with real-time data
   */
  private async renderDashboard(props: any): Promise<string> {
    const { summary, recentOrders, analytics } = props

    return `
      <div class="min-h-screen bg-slate-900 text-white">
        <div class="container mx-auto px-4 py-8">
          <h1 class="text-4xl font-bold mb-8 bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent">
            Enterprise Dashboard
          </h1>

          <!-- Summary Cards -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-slate-800 rounded-lg p-6 border border-slate-700">
              <h3 class="text-lg font-semibold text-blue-400 mb-2">Total Users</h3>
              <p class="text-3xl font-bold">${summary.totalUsers.toLocaleString()}</p>
              <p class="text-sm text-slate-400 mt-1">+${summary.newUsers} this week</p>
            </div>
            <div class="bg-slate-800 rounded-lg p-6 border border-slate-700">
              <h3 class="text-lg font-semibold text-green-400 mb-2">Revenue</h3>
              <p class="text-3xl font-bold">$${(summary.totalRevenue / 1000).toFixed(1)}K</p>
              <p class="text-sm text-slate-400 mt-1">+${summary.revenueGrowth}% vs last month</p>
            </div>
            <div class="bg-slate-800 rounded-lg p-6 border border-slate-700">
              <h3 class="text-lg font-semibold text-purple-400 mb-2">Orders</h3>
              <p class="text-3xl font-bold">${summary.totalOrders.toLocaleString()}</p>
              <p class="text-sm text-slate-400 mt-1">${summary.pendingOrders} pending</p>
            </div>
            <div class="bg-slate-800 rounded-lg p-6 border border-slate-700">
              <h3 class="text-lg font-semibold text-cyan-400 mb-2">Products</h3>
              <p class="text-3xl font-bold">${summary.totalProducts.toLocaleString()}</p>
              <p class="text-sm text-slate-400 mt-1">${summary.lowStockProducts} low stock</p>
            </div>
          </div>

          <!-- Recent Orders -->
          <div class="bg-slate-800 rounded-lg p-6 mb-8 border border-slate-700">
            <h2 class="text-2xl font-bold mb-4">Recent Orders</h2>
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead>
                  <tr class="border-b border-slate-700">
                    <th class="text-left py-2">Order ID</th>
                    <th class="text-left py-2">Customer</th>
                    <th class="text-left py-2">Total</th>
                    <th class="text-left py-2">Status</th>
                    <th class="text-left py-2">Date</th>
                  </tr>
                </thead>
                <tbody>
                  ${recentOrders.slice(0, 10).map((order: any) => `
                    <tr class="border-b border-slate-700/50 hover:bg-slate-700/30">
                      <td class="py-3 font-mono text-sm">${order.id}</td>
                      <td class="py-3">${order.user_name}</td>
                      <td class="py-3 font-semibold">$${order.total}</td>
                      <td class="py-3">
                        <span class="px-2 py-1 rounded text-xs ${this.getStatusColor(order.status)}">
                          ${order.status}
                        </span>
                      </td>
                      <td class="py-3 text-sm text-slate-400">${new Date(order.created_at).toLocaleDateString()}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>
          </div>

          <!-- Analytics Chart Placeholder -->
          <div class="bg-slate-800 rounded-lg p-6 border border-slate-700">
            <h2 class="text-2xl font-bold mb-4">Revenue Analytics</h2>
            <div class="h-64 flex items-center justify-center text-slate-400">
              <div class="text-center">
                <div class="text-4xl mb-2">📊</div>
                <p>Analytics chart would be rendered here</p>
                <p class="text-sm">Real-time data: ${analytics.length} data points</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    `
  }

  /**
   * Get server-side props for dashboard (SSR)
   */
  private async getDashboardProps(context: RenderContext): Promise<any> {
    await this.orm.connect()

    const [
      totalUsers,
      totalRevenue,
      totalOrders,
      totalProducts,
      recentOrders,
      analytics
    ] = await Promise.all([
      this.orm.query('SELECT COUNT(*) as count FROM users'),
      this.orm.query('SELECT SUM(total) as revenue FROM orders WHERE status IN (?, ?)', ['completed', 'delivered']),
      this.orm.query('SELECT COUNT(*) as count FROM orders'),
      this.orm.query('SELECT COUNT(*) as count FROM products'),
      this.orm.query(`
        SELECT o.*, u.name as user_name
        FROM orders o
        LEFT JOIN users u ON o.user_id = u.id
        ORDER BY o.created_at DESC
        LIMIT 20
      `),
      this.orm.query(`
        SELECT DATE(created_at) as date, COUNT(*) as orders, SUM(total) as revenue
        FROM orders
        WHERE created_at >= DATE('now', '-30 days')
        GROUP BY DATE(created_at)
        ORDER BY date DESC
      `)
    ])

    const summary = {
      totalUsers: totalUsers.rows[0]?.count || 0,
      totalRevenue: totalRevenue.rows[0]?.revenue || 0,
      totalOrders: totalOrders.rows[0]?.count || 0,
      totalProducts: totalProducts.rows[0]?.count || 0,
      newUsers: Math.floor(Math.random() * 50) + 10,
      revenueGrowth: Math.floor(Math.random() * 20) + 5,
      pendingOrders: Math.floor(Math.random() * 20) + 5,
      lowStockProducts: Math.floor(Math.random() * 10) + 2
    }

    return {
      summary,
      recentOrders: recentOrders.rows,
      analytics: analytics.rows,
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Product page component (ISR)
   */
  private async renderProductPage(props: any): Promise<string> {
    const { product, relatedProducts, reviews } = props

    return `
      <div class="min-h-screen bg-slate-900 text-white">
        <div class="container mx-auto px-4 py-8">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Product Image -->
            <div class="bg-slate-800 rounded-lg p-6 border border-slate-700">
              <div class="aspect-square bg-slate-700 rounded-lg flex items-center justify-center">
                <span class="text-6xl">📱</span>
              </div>
            </div>

            <!-- Product Info -->
            <div>
              <h1 class="text-4xl font-bold mb-4">${product.name}</h1>
              <p class="text-slate-300 mb-6">${product.description}</p>

              <div class="mb-6">
                <span class="text-3xl font-bold text-green-400">$${product.price}</span>
                <span class="text-slate-400 ml-2">In stock: ${product.stock}</span>
              </div>

              <div class="flex items-center mb-6">
                <div class="flex text-yellow-400 mr-2">
                  ${'★'.repeat(Math.floor(product.rating))}${'☆'.repeat(5 - Math.floor(product.rating))}
                </div>
                <span class="text-slate-400">${product.rating}/5 (${product.review_count} reviews)</span>
              </div>

              <button class="bg-blue-600 hover:bg-blue-700 px-8 py-3 rounded-lg font-semibold transition-colors">
                Add to Cart
              </button>
            </div>
          </div>

          <!-- Related Products -->
          <div class="mb-8">
            <h2 class="text-2xl font-bold mb-4">Related Products</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              ${relatedProducts.map((related: any) => `
                <div class="bg-slate-800 rounded-lg p-4 border border-slate-700">
                  <div class="aspect-square bg-slate-700 rounded mb-3 flex items-center justify-center">
                    <span class="text-2xl">📱</span>
                  </div>
                  <h3 class="font-semibold mb-1">${related.name}</h3>
                  <p class="text-green-400 font-bold">$${related.price}</p>
                </div>
              `).join('')}
            </div>
          </div>
        </div>
      </div>
    `
  }

  /**
   * Get static props for product page (ISR)
   */
  private async getProductStaticProps(context: RenderContext): Promise<any> {
    await this.orm.connect()

    const productId = context.params.id
    const [product, relatedProducts] = await Promise.all([
      this.orm.query('SELECT * FROM products WHERE id = ?', [productId]),
      this.orm.query('SELECT * FROM products WHERE id != ? ORDER BY RANDOM() LIMIT 4', [productId])
    ])

    if (!product.rows[0]) {
      throw new Error('Product not found')
    }

    return {
      product: product.rows[0],
      relatedProducts: relatedProducts.rows,
      reviews: [], // Would fetch reviews here
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Get static paths for product pages
   */
  private async getProductStaticPaths(): Promise<{ paths: string[]; fallback: boolean | 'blocking' }> {
    await this.orm.connect()

    const products = await this.orm.query('SELECT id FROM products LIMIT 100')
    const paths = products.rows.map(product => `/products/${product.id}`)

    return {
      paths,
      fallback: 'blocking' // Generate other pages on-demand
    }
  }

  /**
   * Helper methods
   */
  private getStatusColor(status: string): string {
    const colors = {
      'completed': 'bg-green-600',
      'pending': 'bg-yellow-600',
      'shipped': 'bg-blue-600',
      'cancelled': 'bg-red-600',
      'delivered': 'bg-green-600'
    }
    return colors[status as keyof typeof colors] || 'bg-gray-600'
  }

  private wrapWithLayout(html: string, layout?: string): string {
    // Simple layout wrapper - could be more sophisticated
    return html
  }

  private generateHead(props: any, context: RenderContext): string {
    return `
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Kilat.js - Universal Framework</title>
        <meta name="description" content="The most powerful universal full-stack framework">
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
          @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: .5; } }
          .animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
        </style>
      </head>
    `
  }

  private generateScripts(component: PageComponent, props: any, mode: RenderMode): string[] {
    const scripts = ['/js/kilat-runtime.js']

    if (mode === 'csr' || this.config.hydration.enabled) {
      scripts.push('/js/kilat-hydration.js')
    }

    if (mode === 'isr') {
      scripts.push('/js/kilat-isr.js')
    }

    return scripts
  }

  private generateStyles(component: PageComponent, props: any): string[] {
    return ['/css/kilat-core.css']
  }

  private generateMeta(props: any, context: RenderContext): any {
    return {
      title: 'Kilat.js - Universal Framework',
      description: 'The most powerful universal full-stack framework',
      keywords: ['framework', 'universal', 'ssr', 'ssg', 'csr', 'isr']
    }
  }

  private render404(context: RenderContext): Promise<RenderResult> {
    return Promise.resolve({
      html: '<div class="min-h-screen bg-slate-900 text-white flex items-center justify-center"><h1 class="text-4xl">404 - Page Not Found</h1></div>',
      head: this.generateHead({}, context),
      scripts: [],
      styles: [],
      data: {},
      meta: { title: '404 - Not Found', description: '', keywords: [] },
      performance: { renderTime: 0, dataFetchTime: 0, cacheHit: false, mode: 'ssr' }
    })
  }

  private renderError(error: Error, context: RenderContext): Promise<RenderResult> {
    return Promise.resolve({
      html: `<div class="min-h-screen bg-slate-900 text-white flex items-center justify-center"><div class="text-center"><h1 class="text-4xl text-red-400 mb-4">Error</h1><p>${error.message}</p></div></div>`,
      head: this.generateHead({}, context),
      scripts: [],
      styles: [],
      data: { error: error.message },
      meta: { title: 'Error', description: '', keywords: [] },
      performance: { renderTime: 0, dataFetchTime: 0, cacheHit: false, mode: 'ssr' }
    })
  }

  // Additional component implementations for User Profile, Blog Post, etc.
  private async renderUserProfile(props: any): Promise<string> { return '<div>User Profile</div>' }
  private async getUserProfileProps(context: RenderContext): Promise<any> { return {} }
  private async renderBlogPost(props: any): Promise<string> { return '<div>Blog Post</div>' }
  private async getBlogStaticProps(context: RenderContext): Promise<any> { return {} }
  private async getBlogStaticPaths(): Promise<{ paths: string[]; fallback: boolean | 'blocking' }> {
    return { paths: [], fallback: false }
  }
}
}
