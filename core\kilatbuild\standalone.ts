/**
 * KilatBuild - Standalone Build System
 * Independent build system without Next.js or Vite
 */

import { readFileSync, writeFileSync, existsSync, mkdirSync, readdirSync, statSync } from 'fs'
import { join, extname, dirname, relative, resolve } from 'path'
import { createHash } from 'crypto'

export interface BuildConfig {
  entry: string
  outDir: string
  publicDir?: string
  minify?: boolean
  sourcemap?: boolean
  target?: 'es2020' | 'es2022' | 'esnext'
  external?: string[]
  define?: Record<string, string>
  plugins?: BuildPlugin[]
}

export interface BuildPlugin {
  name: string
  setup: (build: BuildContext) => void
}

export interface BuildContext {
  config: BuildConfig
  addFile: (path: string, content: string) => void
  resolveImport: (specifier: string, importer: string) => string | null
  transform: (code: string, path: string) => string
}

export interface BuildResult {
  success: boolean
  files: Map<string, string>
  errors: string[]
  warnings: string[]
  stats: {
    totalSize: number
    gzipSize: number
    files: number
    duration: number
  }
}

export class KilatStandaloneBuild {
  private config: BuildConfig
  private files: Map<string, string> = new Map()
  private dependencies: Set<string> = new Set()
  private errors: string[] = []
  private warnings: string[] = []

  constructor(config: BuildConfig) {
    this.config = {
      minify: true,
      sourcemap: true,
      target: 'es2022',
      external: [],
      define: {},
      plugins: [],
      ...config
    }
  }

  /**
   * Build the application
   */
  async build(): Promise<BuildResult> {
    const startTime = Date.now()
    
    try {
      console.log('🏗️ Starting Kilat.js standalone build...')
      
      // Clear previous build
      this.files.clear()
      this.dependencies.clear()
      this.errors = []
      this.warnings = []

      // Create build context
      const context: BuildContext = {
        config: this.config,
        addFile: this.addFile.bind(this),
        resolveImport: this.resolveImport.bind(this),
        transform: this.transform.bind(this)
      }

      // Run plugins setup
      for (const plugin of this.config.plugins || []) {
        plugin.setup(context)
      }

      // Build entry point
      await this.buildEntry()

      // Process dependencies
      await this.processDependencies()

      // Copy public assets
      if (this.config.publicDir && existsSync(this.config.publicDir)) {
        this.copyPublicAssets()
      }

      // Generate HTML
      this.generateHTML()

      // Write files to disk
      this.writeFiles()

      const duration = Date.now() - startTime
      const stats = this.calculateStats()

      console.log(`✅ Build completed in ${duration}ms`)
      console.log(`📦 Generated ${stats.files} files (${this.formatSize(stats.totalSize)})`)

      return {
        success: true,
        files: this.files,
        errors: this.errors,
        warnings: this.warnings,
        stats: { ...stats, duration }
      }

    } catch (error) {
      this.errors.push(error instanceof Error ? error.message : String(error))
      
      return {
        success: false,
        files: this.files,
        errors: this.errors,
        warnings: this.warnings,
        stats: {
          totalSize: 0,
          gzipSize: 0,
          files: 0,
          duration: Date.now() - startTime
        }
      }
    }
  }

  /**
   * Build entry point
   */
  private async buildEntry(): Promise<void> {
    const entryPath = resolve(this.config.entry)
    
    if (!existsSync(entryPath)) {
      throw new Error(`Entry file not found: ${entryPath}`)
    }

    const content = readFileSync(entryPath, 'utf-8')
    const transformed = this.transform(content, entryPath)
    
    this.addFile('main.js', transformed)
    this.extractDependencies(content, entryPath)
  }

  /**
   * Process dependencies
   */
  private async processDependencies(): Promise<void> {
    const processed = new Set<string>()
    
    for (const dep of this.dependencies) {
      if (processed.has(dep)) continue
      processed.add(dep)

      try {
        const content = readFileSync(dep, 'utf-8')
        const transformed = this.transform(content, dep)
        const relativePath = relative(process.cwd(), dep)
        const outputPath = relativePath.replace(/\.(ts|tsx)$/, '.js')
        
        this.addFile(outputPath, transformed)
        this.extractDependencies(content, dep)
      } catch (error) {
        this.warnings.push(`Failed to process dependency: ${dep}`)
      }
    }
  }

  /**
   * Extract dependencies from code
   */
  private extractDependencies(code: string, importer: string): void {
    const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g
    const requireRegex = /require\(['"]([^'"]+)['"]\)/g
    
    let match
    
    // Extract ES6 imports
    while ((match = importRegex.exec(code)) !== null) {
      const specifier = match[1]
      const resolved = this.resolveImport(specifier, importer)
      if (resolved) {
        this.dependencies.add(resolved)
      }
    }
    
    // Extract CommonJS requires
    while ((match = requireRegex.exec(code)) !== null) {
      const specifier = match[1]
      const resolved = this.resolveImport(specifier, importer)
      if (resolved) {
        this.dependencies.add(resolved)
      }
    }
  }

  /**
   * Resolve import specifier
   */
  private resolveImport(specifier: string, importer: string): string | null {
    // Skip external dependencies
    if (this.config.external?.includes(specifier)) {
      return null
    }

    // Skip node modules
    if (!specifier.startsWith('.') && !specifier.startsWith('/')) {
      return null
    }

    const importerDir = dirname(importer)
    let resolved: string

    if (specifier.startsWith('.')) {
      resolved = resolve(importerDir, specifier)
    } else {
      resolved = resolve(specifier)
    }

    // Try different extensions
    const extensions = ['.ts', '.tsx', '.js', '.jsx']
    
    for (const ext of extensions) {
      const withExt = resolved + ext
      if (existsSync(withExt)) {
        return withExt
      }
    }

    // Try index files
    for (const ext of extensions) {
      const indexFile = join(resolved, `index${ext}`)
      if (existsSync(indexFile)) {
        return indexFile
      }
    }

    return null
  }

  /**
   * Transform code
   */
  private transform(code: string, path: string): string {
    let transformed = code

    // Apply defines
    for (const [key, value] of Object.entries(this.config.define || {})) {
      const regex = new RegExp(`\\b${key}\\b`, 'g')
      transformed = transformed.replace(regex, value)
    }

    // Simple TypeScript to JavaScript transformation
    if (path.endsWith('.ts') || path.endsWith('.tsx')) {
      transformed = this.transformTypeScript(transformed)
    }

    // Minify if enabled
    if (this.config.minify) {
      transformed = this.minify(transformed)
    }

    return transformed
  }

  /**
   * Simple TypeScript transformation
   */
  private transformTypeScript(code: string): string {
    return code
      // Remove type annotations
      .replace(/:\s*[A-Za-z_][A-Za-z0-9_<>[\]|&\s]*(?=\s*[=,;)])/g, '')
      // Remove interface declarations
      .replace(/interface\s+\w+\s*{[^}]*}/g, '')
      // Remove type declarations
      .replace(/type\s+\w+\s*=\s*[^;]+;/g, '')
      // Remove export type
      .replace(/export\s+type\s+[^;]+;/g, '')
      // Remove as assertions
      .replace(/\s+as\s+\w+/g, '')
  }

  /**
   * Simple minification
   */
  private minify(code: string): string {
    return code
      // Remove comments
      .replace(/\/\*[\s\S]*?\*\//g, '')
      .replace(/\/\/.*$/gm, '')
      // Remove extra whitespace
      .replace(/\s+/g, ' ')
      .trim()
  }

  /**
   * Copy public assets
   */
  private copyPublicAssets(): void {
    const copyDir = (dir: string, prefix: string = '') => {
      const items = readdirSync(dir)
      
      for (const item of items) {
        const fullPath = join(dir, item)
        const stat = statSync(fullPath)
        
        if (stat.isDirectory()) {
          copyDir(fullPath, join(prefix, item))
        } else {
          const content = readFileSync(fullPath)
          const outputPath = join(prefix, item)
          this.files.set(outputPath, content.toString('base64'))
        }
      }
    }

    copyDir(this.config.publicDir!)
  }

  /**
   * Generate HTML file
   */
  private generateHTML(): void {
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kilat.js App</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <div id="app"></div>
    <script src="./main.js"></script>
</body>
</html>
    `.trim()

    this.addFile('index.html', html)
  }

  /**
   * Add file to build output
   */
  private addFile(path: string, content: string): void {
    this.files.set(path, content)
  }

  /**
   * Write files to disk
   */
  private writeFiles(): void {
    if (!existsSync(this.config.outDir)) {
      mkdirSync(this.config.outDir, { recursive: true })
    }

    for (const [path, content] of this.files) {
      const fullPath = join(this.config.outDir, path)
      const dir = dirname(fullPath)
      
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true })
      }

      writeFileSync(fullPath, content, 'utf-8')
    }
  }

  /**
   * Calculate build statistics
   */
  private calculateStats() {
    let totalSize = 0
    
    for (const content of this.files.values()) {
      totalSize += Buffer.byteLength(content, 'utf-8')
    }

    return {
      totalSize,
      gzipSize: Math.floor(totalSize * 0.3), // Rough estimate
      files: this.files.size
    }
  }

  /**
   * Format file size
   */
  private formatSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`
  }
}
