import { Ki<PERSON><PERSON>ard } from '../components/ui/card' import { <PERSON><PERSON><PERSON>utton } from '../components/ui/button' export default function ProductsPage() { return ( React.createElement('div', null, <div className="animate-kilat-fade-in"> <h1 className="text-3xl font-bold mb-2">Products</h1> <p className="text-glow-muted">Welcome to the products page</p> ) React.createElement('KilatCard', null, <div className="p-6"> <h2 className="text-xl font-semibold mb-4">Content</h2> <p className="text-glow-muted mb-4"> This is a generated page. You can customize it by editing the file at: </p> <code className="bg-glow-surface px-2 py-1 rounded text-sm"> apps/products/page.tsx </code> <div className="mt-6"> <KilatButton variant="primary"> Get Started </KilatButton> </div> </div> ) </div> ) }
//# sourceMappingURL=apps\products\page.tsx.map