/**
 * ProductsService - Business logic for products
 * Handles CRUD operations and business rules
 */

import { KilatORM } from '@/core/kilatorm'
import { generateId } from '@/core/kilatlib/utils'

interface Product {
  id: string
  name: string
  description: string
  price: number
  category: string
  stock: number
  image?: string
  status: string
  createdAt: Date
  updatedAt: Date
}

interface CreateProductData {
  name: string
  description: string
  price: number
  category: string
  stock?: number
  image?: string
}

interface UpdateProductData {
  name?: string
  description?: string
  price?: number
  category?: string
  stock?: number
  image?: string
  status?: string
}

interface GetAllOptions {
  page: number
  limit: number
  search?: string
  category?: string
}

interface GetAllResult {
  items: Product[]
  total: number
}

interface ServiceResult<T = any> {
  success: boolean
  data?: T
  error?: string
}

export class ProductsService {
  private orm: KilatORM
  
  constructor() {
    this.orm = new KilatORM()
    // Initialize database connection
    this.orm.connect()
  }
  
  /**
   * Get all products with pagination and filters
   */
  async getAll(options: GetAllOptions): Promise<GetAllResult> {
    try {
      const { page, limit, search, category } = options
      const offset = (page - 1) * limit
      
      let query: any = { status: 'active' }
      
      if (search) {
        query = {
          ...query,
          $or: [
            { name: { $regex: search, $options: 'i' } },
            { description: { $regex: search, $options: 'i' } }
          ]
        }
      }
      
      if (category) {
        query.category = category
      }
      
      const [items, total] = await Promise.all([
        this.orm.products.find(query)
          .skip(offset)
          .limit(limit)
          .sort({ createdAt: -1 }),
        this.orm.products.countDocuments(query)
      ])
      
      return { items, total }
      
    } catch (error) {
      console.error('Get all products error:', error)
      return { items: [], total: 0 }
    }
  }
  
  /**
   * Get product by ID
   */
  async getById(id: string): Promise<Product | null> {
    try {
      return await this.orm.products.findById(id)
    } catch (error) {
      console.error('Get product by ID error:', error)
      return null
    }
  }
  
  /**
   * Create new product
   */
  async create(data: CreateProductData): Promise<ServiceResult> {
    try {
      const product = await this.orm.products.create({
        id: generateId('prod'),
        ...data,
        stock: data.stock || 0,
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      })
      
      return {
        success: true,
        data: product
      }
      
    } catch (error) {
      console.error('Create product error:', error)
      return {
        success: false,
        error: 'Failed to create product'
      }
    }
  }
  
  /**
   * Update product
   */
  async update(id: string, data: UpdateProductData): Promise<ServiceResult> {
    try {
      const existing = await this.orm.products.findById(id)
      
      if (!existing) {
        return {
          success: false,
          error: 'Product not found'
        }
      }
      
      const updated = await this.orm.products.updateOne(
        { id },
        {
          ...data,
          updatedAt: new Date()
        }
      )
      
      return {
        success: true,
        data: updated
      }
      
    } catch (error) {
      console.error('Update product error:', error)
      return {
        success: false,
        error: 'Failed to update product'
      }
    }
  }
  
  /**
   * Delete product
   */
  async delete(id: string): Promise<ServiceResult> {
    try {
      const existing = await this.orm.products.findById(id)
      
      if (!existing) {
        return {
          success: false,
          error: 'Product not found'
        }
      }
      
      // Soft delete by updating status
      await this.orm.products.updateOne(
        { id },
        { 
          status: 'deleted',
          updatedAt: new Date()
        }
      )
      
      return {
        success: true
      }
      
    } catch (error) {
      console.error('Delete product error:', error)
      return {
        success: false,
        error: 'Failed to delete product'
      }
    }
  }
  
  /**
   * Update product stock
   */
  async updateStock(id: string, quantity: number): Promise<ServiceResult> {
    try {
      const product = await this.orm.products.findById(id)
      
      if (!product) {
        return {
          success: false,
          error: 'Product not found'
        }
      }
      
      const newStock = product.stock + quantity
      
      if (newStock < 0) {
        return {
          success: false,
          error: 'Insufficient stock'
        }
      }
      
      const updated = await this.orm.products.updateOne(
        { id },
        {
          stock: newStock,
          updatedAt: new Date()
        }
      )
      
      return {
        success: true,
        data: updated
      }
      
    } catch (error) {
      console.error('Update stock error:', error)
      return {
        success: false,
        error: 'Failed to update stock'
      }
    }
  }
  
  /**
   * Get products by category
   */
  async getByCategory(category: string): Promise<Product[]> {
    try {
      return await this.orm.products.find({ 
        category, 
        status: 'active' 
      }).sort({ name: 1 })
    } catch (error) {
      console.error('Get products by category error:', error)
      return []
    }
  }
  
  /**
   * Get low stock products
   */
  async getLowStock(threshold: number = 10): Promise<Product[]> {
    try {
      return await this.orm.products.find({
        stock: { $lte: threshold },
        status: 'active'
      }).sort({ stock: 1 })
    } catch (error) {
      console.error('Get low stock products error:', error)
      return []
    }
  }
}
