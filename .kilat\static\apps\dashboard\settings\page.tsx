import { Kilat<PERSON>ard } from '../components/ui/card' import { <PERSON><PERSON><PERSON>utton } from '../components/ui/button' export default function DashboardSettingsPage() { return ( React.createElement('div', null, <div className="animate-kilat-fade-in"> <h1 className="text-3xl font-bold mb-2">Settings</h1> <p className="text-glow-muted">Manage your application settings and preferences</p> ) React.createElement('div', null, {} <div className="lg:col-span-1"> <KilatCard className="animate-kilat-slide-up"> <div className="p-6"> <h2 className="text-lg font-semibold mb-4">Settings Categories</h2> <nav className="space-y-2"> <a href="#general" className="block px-4 py-2 rounded-lg bg-glow-primary/10 text-glow-primary font-medium" > 🔧 General </a> <a href="#security" className="block px-4 py-2 rounded-lg hover:bg-glow-surface/50 text-glow-text transition-colors" > 🔒 Security </a> <a href="#notifications" className="block px-4 py-2 rounded-lg hover:bg-glow-surface/50 text-glow-text transition-colors" > 🔔 Notifications </a> <a href="#appearance" className="block px-4 py-2 rounded-lg hover:bg-glow-surface/50 text-glow-text transition-colors" > 🎨 Appearance </a> <a href="#integrations" className="block px-4 py-2 rounded-lg hover:bg-glow-surface/50 text-glow-text transition-colors" > 🔌 Integrations </a> </nav> ) </KilatCard> </div> {} React.createElement('div', null, {} <KilatCard className="animate-kilat-slide-up" style={{ animationDelay: '0.1s' }}> <div className="p-6"> <h3 className="text-xl font-semibold mb-4">General Settings</h3> <div className="space-y-6"> <div> <label className="block text-sm font-medium mb-2">Application Name</label> <input type="text" defaultValue="Kilat.js Dashboard" className="w-full px-4 py-2 bg-glow-surface border border-glow-surface rounded-lg focus:outline-none focus:ring-2 focus:ring-glow-primary/50" /> ) React.createElement('div', null, <label className="block text-sm font-medium mb-2">Description</label> <textarea rows={3} defaultValue="Modern fullstack framework dashboard" className="w-full px-4 py-2 bg-glow-surface border border-glow-surface rounded-lg focus:outline-none focus:ring-2 focus:ring-glow-primary/50" /> ) React.createElement('div', null, <label className="block text-sm font-medium mb-2">Default Language</label> <select className="w-full px-4 py-2 bg-glow-surface border border-glow-surface rounded-lg focus:outline-none focus:ring-2 focus:ring-glow-primary/50"> <option value="en">English</option> <option value="id">Bahasa Indonesia</option> <option value="es">Español</option> <option value="fr">Français</option> </select> ) React.createElement('div', null, <label className="block text-sm font-medium mb-2">Timezone</label> <select className="w-full px-4 py-2 bg-glow-surface border border-glow-surface rounded-lg focus:outline-none focus:ring-2 focus:ring-glow-primary/50"> <option value="UTC">UTC</option> <option value="Asia/Jakarta">Asia/Jakarta</option> <option value="America/New_York">America/New_York</option> <option value="Europe/London">Europe/London</option> </select> ) </div> </div> </KilatCard> {} React.createElement('KilatCard', null, <div className="p-6"> <h3 className="text-xl font-semibold mb-4">Security Settings</h3> <div className="space-y-6"> <div className="flex items-center justify-between"> <div> <h4 className="font-medium">Two-Factor Authentication</h4> <p className="text-sm text-glow-muted">Add an extra layer of security to your account</p> </div> <label className="relative inline-flex items-center cursor-pointer"> <input type="checkbox" className="sr-only peer" /> <div className="w-11 h-6 bg-glow-surface peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-glow-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-glow-primary"></div> </label> </div> <div className="flex items-center justify-between"> <div> <h4 className="font-medium">Session Timeout</h4> <p className="text-sm text-glow-muted">Automatically log out after inactivity</p> </div> <select className="px-4 py-2 bg-glow-surface border border-glow-surface rounded-lg focus:outline-none focus:ring-2 focus:ring-glow-primary/50"> <option value="30">30 minutes</option> <option value="60">1 hour</option> <option value="120">2 hours</option> <option value="480">8 hours</option> </select> </div> <div className="flex items-center justify-between"> <div> <h4 className="font-medium">Login Notifications</h4> <p className="text-sm text-glow-muted">Get notified of new login attempts</p> </div> <label className="relative inline-flex items-center cursor-pointer"> <input type="checkbox" className="sr-only peer" defaultChecked /> <div className="w-11 h-6 bg-glow-surface peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-glow-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-glow-primary"></div> </label> </div> </div> </div> ) {} React.createElement('KilatCard', null, <div className="p-6"> <h3 className="text-xl font-semibold mb-4">Appearance</h3> <div className="space-y-6"> <div> <label className="block text-sm font-medium mb-3">Theme</label> <div className="grid grid-cols-2 gap-4"> <div className="relative"> <input type="radio" name="theme" value="glow" id="theme-glow" className="sr-only peer" defaultChecked /> <label htmlFor="theme-glow" className="block p-4 border-2 border-glow-surface rounded-lg cursor-pointer peer-checked:border-glow-primary peer-checked:bg-glow-primary/10 transition-colors" > <div className="flex items-center space-x-3"> <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full"></div> <div> <p className="font-medium">Glow</p> <p className="text-sm text-glow-muted">Dark theme with blue accents</p> </div> </div> </label> </div> <div className="relative"> <input type="radio" name="theme" value="cyber" id="theme-cyber" className="sr-only peer" /> <label htmlFor="theme-cyber" className="block p-4 border-2 border-glow-surface rounded-lg cursor-pointer peer-checked:border-glow-primary peer-checked:bg-glow-primary/10 transition-colors" > <div className="flex items-center space-x-3"> <div className="w-6 h-6 bg-gradient-to-br from-green-400 to-pink-500 rounded-full"></div> <div> <p className="font-medium">Cyber</p> <p className="text-sm text-glow-muted">Futuristic neon theme</p> </div> </div> </label> </div> </div> </div> <div className="flex items-center justify-between"> <div> <h4 className="font-medium">Animations</h4> <p className="text-sm text-glow-muted">Enable smooth animations and transitions</p> </div> <label className="relative inline-flex items-center cursor-pointer"> <input type="checkbox" className="sr-only peer" defaultChecked /> <div className="w-11 h-6 bg-glow-surface peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-glow-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-glow-primary"></div> </label> </div> </div> </div> ) {} React.createElement('div', null, <KilatButton variant="outline"> Reset to Defaults </KilatButton> <KilatButton variant="primary"> Save Changes </KilatButton> ) </div> </div> </div> ) }
//# sourceMappingURL=apps\dashboard\settings\page.tsx.map