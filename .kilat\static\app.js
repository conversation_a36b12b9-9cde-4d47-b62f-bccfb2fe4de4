(function(){const modules={};const cache={};function require(id){if(cache[id])return cache[id].exports;const module=cache[id]={exports:{}};modules[id].call(module.exports,module,module.exports,require);return module.exports;}window.__kilat_require__=require;window.__kilat_define__=function(id,factory){modules[id]=factory;};})();__kilat_define__("apps_about_page_tsx",function(module,exports,require){import{KilatCard}from '@/components/ui/card' import{KilatButton}from '@/components/ui/button' export default function AboutPage(){return(<div className="space-y-8"><div className="animate-kilat-fade-in"><h1 className="text-3xl font-bold mb-2">About</h1><p className="text-glow-muted">Welcome to the about page</p></div><KilatCard className="animate-kilat-slide-up"><div className="p-6"><h2 className="text-xl font-semibold mb-4">Content</h2><p className="text-glow-muted mb-4">This is a generated page. You can customize it by editing the file at:</p><code className="bg-glow-surface px-2 py-1 rounded text-sm">apps/about/page.tsx</code><div className="mt-6"><KilatButton variant="primary">Get Started</KilatButton></div></div></KilatCard></div>)}});__kilat_define__("apps_dashboard_page_tsx",function(module,exports,require){import{KilatCard}from '@/components/ui/card' export default function DashboardPage(){return(<div className="space-y-8"><div className="animate-kilat-fade-in"><h1 className="text-3xl font-bold mb-2">Dashboard Overview</h1><p className="text-glow-muted">Welcome to your Kilat.js dashboard</p></div>{}<div className="grid md:grid-cols-4 gap-6"><KilatCard className="animate-kilat-slide-up"><div className="p-6"><div className="flex items-center justify-between"><div><p className="text-sm text-glow-muted">Total Users</p><p className="text-2xl font-bold text-glow-primary">1,234</p></div><div className="w-12 h-12 bg-glow-primary/20 rounded-lg flex items-center justify-center">👥</div></div></div></KilatCard><KilatCard className="animate-kilat-slide-up" style={{animationDelay: '0.1s'}}><div className="p-6"><div className="flex items-center justify-between"><div><p className="text-sm text-glow-muted">Active Sessions</p><p className="text-2xl font-bold text-glow-secondary">567</p></div><div className="w-12 h-12 bg-glow-secondary/20 rounded-lg flex items-center justify-center">🔥</div></div></div></KilatCard><KilatCard className="animate-kilat-slide-up" style={{animationDelay: '0.2s'}}><div className="p-6"><div className="flex items-center justify-between"><div><p className="text-sm text-glow-muted">API Requests</p><p className="text-2xl font-bold text-glow-accent">89.2K</p></div><div className="w-12 h-12 bg-glow-accent/20 rounded-lg flex items-center justify-center">📡</div></div></div></KilatCard><KilatCard className="animate-kilat-slide-up" style={{animationDelay: '0.3s'}}><div className="p-6"><div className="flex items-center justify-between"><div><p className="text-sm text-glow-muted">Performance</p><p className="text-2xl font-bold text-green-400">99.9%</p></div><div className="w-12 h-12 bg-green-400/20 rounded-lg flex items-center justify-center">⚡</div></div></div></KilatCard></div>{}<div className="grid md:grid-cols-2 gap-8"><KilatCard className="animate-kilat-scale-in"><div className="p-6"><h2 className="text-xl font-semibold mb-4">Recent Activity</h2><div className="space-y-4"><div className="flex items-center space-x-3"><div className="w-2 h-2 bg-glow-primary rounded-full"></div><span className="text-sm text-glow-muted">New user registered</span><span className="text-xs text-glow-muted ml-auto">2 min ago</span></div><div className="flex items-center space-x-3"><div className="w-2 h-2 bg-glow-secondary rounded-full"></div><span className="text-sm text-glow-muted">API endpoint updated</span><span className="text-xs text-glow-muted ml-auto">5 min ago</span></div><div className="flex items-center space-x-3"><div className="w-2 h-2 bg-glow-accent rounded-full"></div><span className="text-sm text-glow-muted">Database backup completed</span><span className="text-xs text-glow-muted ml-auto">1 hour ago</span></div></div></div></KilatCard><KilatCard className="animate-kilat-scale-in" style={{animationDelay: '0.1s'}}><div className="p-6"><h2 className="text-xl font-semibold mb-4">System Status</h2><div className="space-y-4"><div className="flex items-center justify-between"><span className="text-sm">SpeedRun Runtime</span><span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">Online</span></div><div className="flex items-center justify-between"><span className="text-sm">KilatPack Build</span><span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">Ready</span></div><div className="flex items-center justify-between"><span className="text-sm">Database</span><span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">Connected</span></div><div className="flex items-center justify-between"><span className="text-sm">Cache Layer</span><span className="text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded">Warming</span></div></div></div></KilatCard></div></div>)}});__kilat_define__("apps_dashboard_settings_page_tsx",function(module,exports,require){import{KilatCard}from '@/components/ui/card' import{KilatButton}from '@/components/ui/button' export default function DashboardSettingsPage(){return(<div className="space-y-8"><div className="animate-kilat-fade-in"><h1 className="text-3xl font-bold mb-2">Settings</h1><p className="text-glow-muted">Manage your application settings and preferences</p></div><div className="grid lg:grid-cols-3 gap-8">{}<div className="lg:col-span-1"><KilatCard className="animate-kilat-slide-up"><div className="p-6"><h2 className="text-lg font-semibold mb-4">Settings Categories</h2><nav className="space-y-2"><a href="#general" className="block px-4 py-2 rounded-lg bg-glow-primary/10 text-glow-primary font-medium">🔧 General</a><a href="#security" className="block px-4 py-2 rounded-lg hover:bg-glow-surface/50 text-glow-text transition-colors">🔒 Security</a><a href="#notifications" className="block px-4 py-2 rounded-lg hover:bg-glow-surface/50 text-glow-text transition-colors">🔔 Notifications</a><a href="#appearance" className="block px-4 py-2 rounded-lg hover:bg-glow-surface/50 text-glow-text transition-colors">🎨 Appearance</a><a href="#integrations" className="block px-4 py-2 rounded-lg hover:bg-glow-surface/50 text-glow-text transition-colors">🔌 Integrations</a></nav></div></KilatCard></div>{}<div className="lg:col-span-2 space-y-6">{}<KilatCard className="animate-kilat-slide-up" style={{animationDelay: '0.1s'}}><div className="p-6"><h3 className="text-xl font-semibold mb-4">General Settings</h3><div className="space-y-6"><div><label className="block text-sm font-medium mb-2">Application Name</label><input type="text" defaultValue="Kilat.js Dashboard" className="w-full px-4 py-2 bg-glow-surface border border-glow-surface rounded-lg focus:outline-none focus:ring-2 focus:ring-glow-primary/50"/></div><div><label className="block text-sm font-medium mb-2">Description</label><textarea rows={3}defaultValue="Modern fullstack framework dashboard" className="w-full px-4 py-2 bg-glow-surface border border-glow-surface rounded-lg focus:outline-none focus:ring-2 focus:ring-glow-primary/50"/></div><div><label className="block text-sm font-medium mb-2">Default Language</label><select className="w-full px-4 py-2 bg-glow-surface border border-glow-surface rounded-lg focus:outline-none focus:ring-2 focus:ring-glow-primary/50"><option value="en">English</option><option value="id">Bahasa Indonesia</option><option value="es">Español</option><option value="fr">Français</option></select></div><div><label className="block text-sm font-medium mb-2">Timezone</label><select className="w-full px-4 py-2 bg-glow-surface border border-glow-surface rounded-lg focus:outline-none focus:ring-2 focus:ring-glow-primary/50"><option value="UTC">UTC</option><option value="Asia/Jakarta">Asia/Jakarta</option><option value="America/New_York">America/New_York</option><option value="Europe/London">Europe/London</option></select></div></div></div></KilatCard>{}<KilatCard className="animate-kilat-slide-up" style={{animationDelay: '0.2s'}}><div className="p-6"><h3 className="text-xl font-semibold mb-4">Security Settings</h3><div className="space-y-6"><div className="flex items-center justify-between"><div><h4 className="font-medium">Two-Factor Authentication</h4><p className="text-sm text-glow-muted">Add an extra layer of security to your account</p></div><label className="relative inline-flex items-center cursor-pointer"><input type="checkbox" className="sr-only peer"/><div className="w-11 h-6 bg-glow-surface peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-glow-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-glow-primary"></div></label></div><div className="flex items-center justify-between"><div><h4 className="font-medium">Session Timeout</h4><p className="text-sm text-glow-muted">Automatically log out after inactivity</p></div><select className="px-4 py-2 bg-glow-surface border border-glow-surface rounded-lg focus:outline-none focus:ring-2 focus:ring-glow-primary/50"><option value="30">30 minutes</option><option value="60">1 hour</option><option value="120">2 hours</option><option value="480">8 hours</option></select></div><div className="flex items-center justify-between"><div><h4 className="font-medium">Login Notifications</h4><p className="text-sm text-glow-muted">Get notified of new login attempts</p></div><label className="relative inline-flex items-center cursor-pointer"><input type="checkbox" className="sr-only peer" defaultChecked/><div className="w-11 h-6 bg-glow-surface peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-glow-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-glow-primary"></div></label></div></div></div></KilatCard>{}<KilatCard className="animate-kilat-slide-up" style={{animationDelay: '0.3s'}}><div className="p-6"><h3 className="text-xl font-semibold mb-4">Appearance</h3><div className="space-y-6"><div><label className="block text-sm font-medium mb-3">Theme</label><div className="grid grid-cols-2 gap-4"><div className="relative"><input type="radio" name="theme" value="glow" id="theme-glow" className="sr-only peer" defaultChecked/><label htmlFor="theme-glow" className="block p-4 border-2 border-glow-surface rounded-lg cursor-pointer peer-checked:border-glow-primary peer-checked:bg-glow-primary/10 transition-colors"><div className="flex items-center space-x-3"><div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full"></div><div><p className="font-medium">Glow</p><p className="text-sm text-glow-muted">Dark theme with blue accents</p></div></div></label></div><div className="relative"><input type="radio" name="theme" value="cyber" id="theme-cyber" className="sr-only peer"/><label htmlFor="theme-cyber" className="block p-4 border-2 border-glow-surface rounded-lg cursor-pointer peer-checked:border-glow-primary peer-checked:bg-glow-primary/10 transition-colors"><div className="flex items-center space-x-3"><div className="w-6 h-6 bg-gradient-to-br from-green-400 to-pink-500 rounded-full"></div><div><p className="font-medium">Cyber</p><p className="text-sm text-glow-muted">Futuristic neon theme</p></div></div></label></div></div></div><div className="flex items-center justify-between"><div><h4 className="font-medium">Animations</h4><p className="text-sm text-glow-muted">Enable smooth animations and transitions</p></div><label className="relative inline-flex items-center cursor-pointer"><input type="checkbox" className="sr-only peer" defaultChecked/><div className="w-11 h-6 bg-glow-surface peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-glow-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-glow-primary"></div></label></div></div></div></KilatCard>{}<div className="flex justify-end space-x-4 animate-kilat-scale-in" style={{animationDelay: '0.4s'}}><KilatButton variant="outline">Reset to Defaults</KilatButton><KilatButton variant="primary">Save Changes</KilatButton></div></div></div></div>)}});__kilat_define__("apps_dashboard_users_page_tsx",function(module,exports,require){import{KilatCard}from '@/components/ui/card' import{KilatButton}from '@/components/ui/button' export default function DashboardUsersPage(){const users=[{id: '1',name: 'John Doe',email: '<EMAIL>',role: 'admin',status: 'active',lastLogin: '2025-01-09T10:30:00Z'},{id: '2',name: 'Jane Smith',email: '<EMAIL>',role: 'user',status: 'active',lastLogin: '2025-01-08T15:45:00Z'},{id: '3',name: 'Bob Johnson',email: '<EMAIL>',role: 'user',status: 'inactive',lastLogin: '2025-01-05T09:15:00Z'},] return(<div className="space-y-8"><div className="flex items-center justify-between animate-kilat-fade-in"><div><h1 className="text-3xl font-bold mb-2">Users Management</h1><p className="text-glow-muted">Manage user accounts and permissions</p></div><KilatButton variant="primary">Add New User</KilatButton></div>{}<div className="grid md:grid-cols-4 gap-6"><KilatCard className="animate-kilat-slide-up"><div className="p-6"><div className="flex items-center justify-between"><div><p className="text-sm text-glow-muted">Total Users</p><p className="text-2xl font-bold text-glow-primary">{users.length}</p></div><div className="w-12 h-12 bg-glow-primary/20 rounded-lg flex items-center justify-center">👥</div></div></div></KilatCard><KilatCard className="animate-kilat-slide-up" style={{animationDelay: '0.1s'}}><div className="p-6"><div className="flex items-center justify-between"><div><p className="text-sm text-glow-muted">Active Users</p><p className="text-2xl font-bold text-glow-secondary">{users.filter(u=>u.status==='active').length}</p></div><div className="w-12 h-12 bg-glow-secondary/20 rounded-lg flex items-center justify-center">✅</div></div></div></KilatCard><KilatCard className="animate-kilat-slide-up" style={{animationDelay: '0.2s'}}><div className="p-6"><div className="flex items-center justify-between"><div><p className="text-sm text-glow-muted">Admins</p><p className="text-2xl font-bold text-glow-accent">{users.filter(u=>u.role==='admin').length}</p></div><div className="w-12 h-12 bg-glow-accent/20 rounded-lg flex items-center justify-center">👑</div></div></div></KilatCard><KilatCard className="animate-kilat-slide-up" style={{animationDelay: '0.3s'}}><div className="p-6"><div className="flex items-center justify-between"><div><p className="text-sm text-glow-muted">New This Week</p><p className="text-2xl font-bold text-green-400">2</p></div><div className="w-12 h-12 bg-green-400/20 rounded-lg flex items-center justify-center">📈</div></div></div></KilatCard></div>{}<KilatCard className="animate-kilat-scale-in"><div className="p-6"><div className="flex items-center justify-between mb-6"><h2 className="text-xl font-semibold">All Users</h2><div className="flex items-center space-x-4"><input type="text" placeholder="Search users..." className="px-4 py-2 bg-glow-surface border border-glow-surface rounded-lg focus:outline-none focus:ring-2 focus:ring-glow-primary/50"/><select className="px-4 py-2 bg-glow-surface border border-glow-surface rounded-lg focus:outline-none focus:ring-2 focus:ring-glow-primary/50"><option value="">All Roles</option><option value="admin">Admin</option><option value="user">User</option></select></div></div><div className="overflow-x-auto"><table className="w-full"><thead><tr className="border-b border-glow-surface"><th className="text-left py-3 px-4 font-medium text-glow-muted">User</th><th className="text-left py-3 px-4 font-medium text-glow-muted">Role</th><th className="text-left py-3 px-4 font-medium text-glow-muted">Status</th><th className="text-left py-3 px-4 font-medium text-glow-muted">Last Login</th><th className="text-left py-3 px-4 font-medium text-glow-muted">Actions</th></tr></thead><tbody>{users.map((user,index)=>(<tr key={user.id}className="border-b border-glow-surface/50 hover:bg-glow-surface/30 transition-colors" style={{animationDelay: `${index*0.1}s`}}><td className="py-4 px-4"><div className="flex items-center space-x-3"><div className="w-10 h-10 bg-glow-primary rounded-full flex items-center justify-center text-white font-semibold">{user.name.charAt(0)}</div><div><p className="font-medium">{user.name}</p><p className="text-sm text-glow-muted">{user.email}</p></div></div></td><td className="py-4 px-4"><span className={`px-2 py-1 rounded-full text-xs font-medium ${user.role==='admin' ? 'bg-glow-accent/20 text-glow-accent' : 'bg-glow-secondary/20 text-glow-secondary'}`}>{user.role}</span></td><td className="py-4 px-4"><span className={`px-2 py-1 rounded-full text-xs font-medium ${user.status==='active' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'}`}>{user.status}</span></td><td className="py-4 px-4 text-sm text-glow-muted">{new Date(user.lastLogin).toLocaleDateString()}</td><td className="py-4 px-4"><div className="flex items-center space-x-2"><button className="p-2 hover:bg-glow-surface rounded-lg transition-colors">✏️</button><button className="p-2 hover:bg-glow-surface rounded-lg transition-colors">🗑️</button><button className="p-2 hover:bg-glow-surface rounded-lg transition-colors">👁️</button></div></td></tr>))}</tbody></table></div>{}<div className="flex items-center justify-between mt-6"><p className="text-sm text-glow-muted">Showing 1 to{users.length}of{users.length}users</p><div className="flex items-center space-x-2"><KilatButton variant="outline" size="sm" disabled>Previous</KilatButton><KilatButton variant="primary" size="sm">1</KilatButton><KilatButton variant="outline" size="sm" disabled>Next</KilatButton></div></div></div></KilatCard></div>)}});__kilat_define__("apps_page_tsx",function(module,exports,require){export default function HomePage(){return(<div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white overflow-hidden">{}<section className="relative py-20 px-4 min-h-screen flex items-center"><div className="container mx-auto text-center relative z-10"><div className="animate-kilat-fade-in">{}<div className="mb-8"><div className="inline-flex items-center justify-center w-24 h-24 mb-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 animate-kilat-glow"><span className="text-4xl font-bold">⚡</span></div></div>{}<h1 className="text-7xl md:text-9xl font-black mb-6 bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 bg-clip-text text-transparent animate-kilat-gradient bg-300 leading-tight">Kilat.js</h1>{}<p className="text-xl md:text-3xl text-slate-300 mb-4 max-w-4xl mx-auto font-light leading-relaxed">The<span className="text-blue-400 font-semibold">lightning-fast</span>fullstack framework</p><p className="text-lg md:text-xl text-slate-400 mb-12 max-w-3xl mx-auto">Built from scratch with<span className="text-purple-400">Bun.js</span>,<span className="text-cyan-400">file-based routing</span>,and<span className="text-green-400">zero dependencies</span></p>{}<div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16"><button className="group relative px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/25 animate-kilat-scale-in"><span className="relative z-10">Get Started</span><div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-700 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div></button><button className="group px-8 py-4 border-2 border-slate-600 rounded-xl font-semibold text-lg transition-all duration-300 hover:border-blue-400 hover:text-blue-400 hover:shadow-lg hover:shadow-blue-400/20 animate-kilat-scale-in" style={{animationDelay: '0.2s'}}><span className="flex items-center gap-2"><span>View Documentation</span><span className="group-hover:translate-x-1 transition-transform duration-300">→</span></span></button></div>{}<div className="max-w-2xl mx-auto animate-kilat-slide-up" style={{animationDelay: '0.4s'}}><div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-6 text-left"><div className="flex items-center gap-2 mb-4"><div className="w-3 h-3 bg-red-500 rounded-full"></div><div className="w-3 h-3 bg-yellow-500 rounded-full"></div><div className="w-3 h-3 bg-green-500 rounded-full"></div><span className="text-slate-400 text-sm ml-2">Terminal</span></div><pre className="text-green-400 font-mono text-sm md:text-base"><span className="text-slate-500"># Create new Kilat.js app</span><span className="text-blue-400">npm</span>create kilat-app my-app<span className="text-slate-500"># Start development server</span><span className="text-purple-400">cd</span>my-app&&<span className="text-cyan-400">bun</span>dev</pre></div></div></div></div>{}<div className="absolute inset-0 overflow-hidden">{}<div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-kilat-float"></div><div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-kilat-float" style={{animationDelay: '2s'}}></div><div className="absolute top-3/4 left-1/2 w-64 h-64 bg-cyan-500/10 rounded-full blur-3xl animate-kilat-float" style={{animationDelay: '4s'}}></div>{}<div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_110%)]"></div></div></section>{}<section className="py-32 px-4 bg-slate-800/30"><div className="container mx-auto"><div className="text-center mb-20"><h2 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent animate-kilat-slide-up">Why Choose Kilat.js?</h2><p className="text-xl text-slate-400 max-w-3xl mx-auto animate-kilat-slide-up" style={{animationDelay: '0.2s'}}>Experience the next generation of fullstack development with unmatched performance and developer experience</p></div><div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">{}<div className="group relative bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-8 transition-all duration-500 hover:scale-105 hover:border-blue-500/50 hover:shadow-2xl hover:shadow-blue-500/10 animate-kilat-slide-up" style={{animationDelay: '0.3s'}}><div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div><div className="relative z-10"><div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl mb-6 flex items-center justify-center text-2xl animate-kilat-glow">⚡</div><h3 className="text-2xl font-bold mb-4 text-white group-hover:text-blue-400 transition-colors duration-300">Lightning Fast</h3><p className="text-slate-400 leading-relaxed group-hover:text-slate-300 transition-colors duration-300">Built on Bun.js with native HTTP server. Zero dependencies on Express or Vite. Experience pure speed from the ground up with sub-millisecond response times.</p><div className="mt-6 flex items-center text-sm text-blue-400"><span>Learn more</span><span className="ml-2 group-hover:translate-x-1 transition-transform duration-300">→</span></div></div></div>{}<div className="group relative bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-8 transition-all duration-500 hover:scale-105 hover:border-purple-500/50 hover:shadow-2xl hover:shadow-purple-500/10 animate-kilat-slide-up" style={{animationDelay: '0.4s'}}><div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div><div className="relative z-10"><div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl mb-6 flex items-center justify-center text-2xl animate-kilat-glow" style={{animationDelay: '0.5s'}}>🏗️</div><h3 className="text-2xl font-bold mb-4 text-white group-hover:text-purple-400 transition-colors duration-300">Complete Framework</h3><p className="text-slate-400 leading-relaxed group-hover:text-slate-300 transition-colors duration-300">Everything you need in one package: routing,state management,ORM,build tools,and deployment. No more dependency hell or configuration nightmares.</p><div className="mt-6 flex items-center text-sm text-purple-400"><span>Explore features</span><span className="ml-2 group-hover:translate-x-1 transition-transform duration-300">→</span></div></div></div>{}<div className="group relative bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-8 transition-all duration-500 hover:scale-105 hover:border-green-500/50 hover:shadow-2xl hover:shadow-green-500/10 animate-kilat-slide-up" style={{animationDelay: '0.5s'}}><div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div><div className="relative z-10"><div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl mb-6 flex items-center justify-center text-2xl animate-kilat-glow" style={{animationDelay: '1s'}}>🎨</div><h3 className="text-2xl font-bold mb-4 text-white group-hover:text-green-400 transition-colors duration-300">Amazing DX</h3><p className="text-slate-400 leading-relaxed group-hover:text-slate-300 transition-colors duration-300">File-based routing,instant hot reload,built-in themes,powerful CLI generators,and TypeScript-first development. Joy in every keystroke.</p><div className="mt-6 flex items-center text-sm text-green-400"><span>See examples</span><span className="ml-2 group-hover:translate-x-1 transition-transform duration-300">→</span></div></div></div></div></div></section>{}<section className="py-32 px-4"><div className="container mx-auto"><div className="text-center mb-20"><h2 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent animate-kilat-slide-up">Performance That Speaks</h2><p className="text-xl text-slate-400 max-w-3xl mx-auto animate-kilat-slide-up" style={{animationDelay: '0.2s'}}>Numbers don't lie. See how Kilat.js outperforms traditional frameworks</p></div><div className="grid md:grid-cols-4 gap-8 max-w-5xl mx-auto"><div className="text-center animate-kilat-scale-in" style={{animationDelay: '0.3s'}}><div className="text-5xl md:text-6xl font-black bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent mb-4 animate-kilat-pulse">&lt;10ms</div><p className="text-slate-300 font-semibold mb-2">Cold Start</p><p className="text-slate-500 text-sm">Lightning fast server startup</p></div><div className="text-center animate-kilat-scale-in" style={{animationDelay: '0.4s'}}><div className="text-5xl md:text-6xl font-black bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-4 animate-kilat-pulse" style={{animationDelay: '0.5s'}}>0.1s</div><p className="text-slate-300 font-semibold mb-2">Hot Reload</p><p className="text-slate-500 text-sm">Instant development feedback</p></div><div className="text-center animate-kilat-scale-in" style={{animationDelay: '0.5s'}}><div className="text-5xl md:text-6xl font-black bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent mb-4 animate-kilat-pulse" style={{animationDelay: '1s'}}>50KB</div><p className="text-slate-300 font-semibold mb-2">Bundle Size</p><p className="text-slate-500 text-sm">Minimal production footprint</p></div><div className="text-center animate-kilat-scale-in" style={{animationDelay: '0.6s'}}><div className="text-5xl md:text-6xl font-black bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-4 animate-kilat-pulse" style={{animationDelay: '1.5s'}}>100%</div><p className="text-slate-300 font-semibold mb-2">TypeScript</p><p className="text-slate-500 text-sm">Full type safety out of the box</p></div></div></div></section>{}<section className="py-32 px-4 bg-slate-800/30"><div className="container mx-auto"><div className="text-center mb-20"><h2 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent animate-kilat-slide-up">See It In Action</h2><p className="text-xl text-slate-400 max-w-3xl mx-auto animate-kilat-slide-up" style={{animationDelay: '0.2s'}}>From zero to production in minutes. Experience the simplicity of Kilat.js</p></div><div className="max-w-4xl mx-auto"><div className="bg-slate-900/80 backdrop-blur-sm border border-slate-700 rounded-3xl overflow-hidden animate-kilat-scale-in" style={{animationDelay: '0.4s'}}>{}<div className="flex items-center gap-2 px-6 py-4 bg-slate-800/50 border-b border-slate-700"><div className="w-3 h-3 bg-red-500 rounded-full"></div><div className="w-3 h-3 bg-yellow-500 rounded-full"></div><div className="w-3 h-3 bg-green-500 rounded-full"></div><span className="text-slate-400 text-sm ml-4">~/my-kilat-app</span></div>{}<div className="p-8"><div className="space-y-6">{}<div className="animate-kilat-fade-in" style={{animationDelay: '0.6s'}}><div className="text-slate-500 text-sm mb-2"># Create a new Kilat.js application</div><div className="font-mono text-lg"><span className="text-blue-400">npm</span><span className="text-white">create</span><span className="text-green-400">kilat-app</span><span className="text-purple-400">my-app</span></div></div>{}<div className="animate-kilat-fade-in" style={{animationDelay: '0.8s'}}><div className="text-slate-500 text-sm mb-2"># Navigate and start development</div><div className="font-mono text-lg"><span className="text-cyan-400">cd</span><span className="text-purple-400">my-app</span><span className="text-slate-500">&&</span><span className="text-orange-400">bun</span><span className="text-green-400">dev</span></div></div>{}<div className="animate-kilat-fade-in" style={{animationDelay: '1s'}}><div className="bg-slate-800/50 rounded-xl p-4 border border-slate-600"><div className="text-green-400 font-mono text-sm space-y-1"><div>⚡ Kilat.js v1.0.0</div><div>🚀 SpeedRun server running on http:<div>🔥 Hot Module Replacement enabled</div><div>✅ Ready in 47ms</div></div></div></div></div></div></div><div className="text-center mt-12 animate-kilat-slide-up" style={{animationDelay: '1.2s'}}><p className="text-slate-400 mb-8">That's it!Your Kilat.js application is ready for development.</p><button className="group px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/25"><span className="flex items-center gap-2"><span>Try It Now</span><span className="group-hover:translate-x-1 transition-transform duration-300">🚀</span></span></button></div></div></div></section>{}<footer className="py-20 px-4 border-t border-slate-800"><div className="container mx-auto text-center"><div className="mb-8"><div className="inline-flex items-center justify-center w-16 h-16 mb-4 rounded-full bg-gradient-to-r from-blue-500 to-purple-600"><span className="text-2xl font-bold">⚡</span></div><h3 className="text-2xl font-bold text-white mb-2">Kilat.js</h3><p className="text-slate-400">Lightning-fast fullstack framework</p></div><div className="flex flex-wrap justify-center gap-8 mb-8 text-slate-400"><a href="#" className="hover:text-blue-400 transition-colors duration-300">Documentation</a><a href="#" className="hover:text-blue-400 transition-colors duration-300">Examples</a><a href="#" className="hover:text-blue-400 transition-colors duration-300">GitHub</a><a href="#" className="hover:text-blue-400 transition-colors duration-300">Community</a><a href="#" className="hover:text-blue-400 transition-colors duration-300">Blog</a></div><div className="text-slate-500 text-sm"><p>&copy;2024 Kilat.js. Built with ⚡ and ❤️</p></div></div></footer></div>)}});__kilat_define__("apps_products_page_tsx",function(module,exports,require){import{KilatCard}from '@/components/ui/card' import{KilatButton}from '@/components/ui/button' export default function ProductsPage(){return(<div className="space-y-8"><div className="animate-kilat-fade-in"><h1 className="text-3xl font-bold mb-2">Products</h1><p className="text-glow-muted">Welcome to the products page</p></div><KilatCard className="animate-kilat-slide-up"><div className="p-6"><h2 className="text-xl font-semibold mb-4">Content</h2><p className="text-glow-muted mb-4">This is a generated page. You can customize it by editing the file at:</p><code className="bg-glow-surface px-2 py-1 rounded text-sm">apps/products/page.tsx</code><div className="mt-6"><KilatButton variant="primary">Get Started</KilatButton></div></div></KilatCard></div>)}});